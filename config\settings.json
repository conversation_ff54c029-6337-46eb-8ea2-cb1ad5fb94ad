{"application": {"name": "Comme<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "author": "CommentHunter Team", "description": "网站评论分析与发布工具", "homepage": "https://github.com/hardusernames-team/CommentHunter", "license": "MIT"}, "database": {"type": "sqlite", "path": "data/comment_hunter.db", "backup_enabled": true, "backup_interval": 86400, "max_backups": 7, "auto_vacuum": true, "journal_mode": "WAL", "synchronous": "NORMAL"}, "system": {"log_level": "INFO", "log_file": "logs/comment_hunter.log", "max_log_size": "10MB", "log_rotation": true, "max_threads": 5, "thread_timeout": 300, "auto_backup": false, "auto_cleanup": false, "cleanup_days": 30, "temp_dir": "temp", "data_dir": "data", "export_dir": "exports"}, "ui": {"theme": "default", "language": "zh_CN", "font_family": "Microsoft YaHei", "font_size": 9, "window_size": {"width": 1200, "height": 800}, "window_position": {"x": 100, "y": 100}, "remember_window_state": true, "show_splash_screen": true, "auto_save_interval": 300, "confirm_exit": true}, "ai": {"provider": "siliconflow", "api_key": "sk-fehvttrlgxgxwomdpegycdldtqlumyxsawgvgblaquzhhrfz", "api_url": "https://api.siliconflow.cn/v1/chat/completions", "model": "Qwen/Qwen2.5-7B-Instruct", "available_models": ["Qwen/Qwen2.5-7B-Instruct", "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B", "Qwen/Qwen3-8B", "THUDM/GLM-Z1-9B-0414", "THUDM/GLM-4-9B-0414", "deepseek-ai/DeepSeek-R1-<PERSON>still-Qwen-7B", "Qwen/Qwen2.5-Coder-7B-Instruct", "internlm/internlm2_5-7b-chat", "Qwen/Qwen2-7B-Instruct", "THUDM/glm-4-9b-chat"], "max_tokens": 1000, "temperature": 0.7, "timeout": 30, "retry_times": 3, "retry_delay": 1, "batch_size": 10, "rate_limit": {"requests_per_minute": 60, "requests_per_hour": 1000}}, "detection": {"timeout": 30, "max_redirects": 5, "verify_ssl": false, "user_agent_rotation": true, "concurrent_requests": 3, "retry_times": 2, "retry_delay": 2, "article_sample_size": 5, "min_score_threshold": 60, "cache_results": true, "cache_duration": 3600}, "publishing": {"use_selenium": true, "headless_browser": true, "browser_timeout": 30, "page_load_timeout": 20, "element_wait_timeout": 10, "form_fill_delay": {"min": 0.5, "max": 2.0}, "submit_delay": {"min": 1.0, "max": 3.0}, "success_wait_time": 5, "max_retries": 3, "retry_delay": 5, "simulate_human": true, "random_mouse_movement": true, "random_scroll": true, "random_typing_speed": true}, "proxy": {"enabled": false, "rotation_enabled": true, "test_on_startup": true, "auto_remove_failed": true, "health_check_interval": 300, "max_failures": 5, "timeout": 10, "test_url": "http://httpbin.org/ip"}, "export": {"default_format": "csv", "include_headers": true, "encoding": "utf-8-sig", "date_format": "%Y-%m-%d %H:%M:%S", "auto_open_file": false, "compress_large_files": true, "max_file_size": "50MB"}, "security": {"encrypt_sensitive_data": false, "mask_api_keys": true, "secure_delete": false, "audit_log": false, "session_timeout": 3600, "max_login_attempts": 5, "lockout_duration": 300}, "performance": {"enable_caching": true, "cache_size": "100MB", "memory_limit": "512MB", "gc_threshold": 1000, "optimize_database": true, "preload_data": false, "lazy_loading": true}, "network": {"connection_timeout": 30, "read_timeout": 60, "max_connections": 100, "keep_alive": true, "connection_pool_size": 10, "dns_cache": true, "ipv6_enabled": false}, "notifications": {"enabled": true, "show_success": true, "show_errors": true, "show_warnings": true, "sound_enabled": false, "desktop_notifications": false, "email_notifications": false, "webhook_notifications": false}, "updates": {"check_for_updates": true, "auto_update": false, "update_channel": "stable", "update_server": "https://github.com/hardusernames-team/CommentHunter", "check_interval": 86400, "download_updates": false, "backup_before_update": true}, "debug": {"enabled": false, "verbose_logging": false, "save_screenshots": false, "save_page_source": false, "debug_selenium": false, "profile_performance": false, "memory_profiling": false}, "advanced": {"custom_css_selectors": {}, "custom_user_agents": [], "custom_headers": {}, "javascript_execution": true, "cookie_handling": true, "session_persistence": true, "request_interception": false, "response_modification": false}}