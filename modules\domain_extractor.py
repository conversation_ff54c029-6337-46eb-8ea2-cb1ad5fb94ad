#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CommentHunter - 域名提取模块
功能：多格式文件导入、智能域名解析、实时预览
"""

import os
import re
import csv
import json
import pandas as pd
from urllib.parse import urlparse
from typing import List, Dict, Tuple, Optional, Union


class DomainExtractor:
    """域名提取器类"""

    def __init__(self):
        """初始化域名提取器"""
        self.supported_formats = ['.txt', '.csv', '.xlsx', '.json']
        self.domain_pattern = re.compile(
            r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)+$'
        )
        # 常见的二级域名后缀
        self.common_slds = {
            'co.uk', 'org.uk', 'me.uk', 'ltd.uk', 'plc.uk',
            'com.cn', 'net.cn', 'org.cn', 'gov.cn', 'edu.cn',
            'com.au', 'net.au', 'org.au', 'edu.au', 'gov.au',
            'co.jp', 'ne.jp', 'or.jp', 'go.jp', 'ac.jp',
            'com.br', 'net.br', 'org.br', 'gov.br', 'edu.br'
        }

    def extract_from_file(self, file_path: str) -> Dict[str, Union[List[Dict], str, int]]:
        """
        从文件中提取域名

        Args:
            file_path: 文件路径

        Returns:
            包含提取结果的字典
        """
        if not os.path.exists(file_path):
            return {
                'success': False,
                'error': '文件不存在',
                'domains': [],
                'total': 0,
                'valid': 0,
                'duplicates': 0
            }

        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext not in self.supported_formats:
            return {
                'success': False,
                'error': f'不支持的文件格式: {file_ext}',
                'domains': [],
                'total': 0,
                'valid': 0,
                'duplicates': 0
            }

        try:
            # 根据文件格式选择解析方法
            if file_ext == '.txt':
                raw_data = self._parse_txt_file(file_path)
            elif file_ext == '.csv':
                raw_data = self._parse_csv_file(file_path)
            elif file_ext == '.xlsx':
                raw_data = self._parse_xlsx_file(file_path)
            elif file_ext == '.json':
                raw_data = self._parse_json_file(file_path)
            else:
                return {
                    'success': False,
                    'error': '未知文件格式',
                    'domains': [],
                    'total': 0,
                    'valid': 0,
                    'duplicates': 0
                }

            # 处理提取的数据
            result = self._process_raw_data(raw_data)
            result['success'] = True
            return result

        except Exception as e:
            return {
                'success': False,
                'error': f'文件解析错误: {str(e)}',
                'domains': [],
                'total': 0,
                'valid': 0,
                'duplicates': 0
            }

    def extract_from_text(self, text: str) -> Dict[str, Union[List[Dict], str, int]]:
        """
        从文本中提取域名

        Args:
            text: 输入文本

        Returns:
            包含提取结果的字典
        """
        if not text or not text.strip():
            return {
                'success': True,
                'domains': [],
                'total': 0,
                'valid': 0,
                'duplicates': 0
            }

        try:
            # 按行分割文本
            lines = text.strip().split('\n')
            raw_data = []

            for line in lines:
                line = line.strip()
                if line:
                    raw_data.append(line)

            # 处理提取的数据
            result = self._process_raw_data(raw_data)
            result['success'] = True
            return result

        except Exception as e:
            return {
                'success': False,
                'error': f'文本解析错误: {str(e)}',
                'domains': [],
                'total': 0,
                'valid': 0,
                'duplicates': 0
            }

    def _parse_txt_file(self, file_path: str) -> List[str]:
        """解析TXT文件"""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

        return [line.strip() for line in lines if line.strip()]

    def _parse_csv_file(self, file_path: str) -> List[str]:
        """解析CSV文件"""
        data = []

        # 尝试不同的编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                    # 尝试自动检测分隔符
                    sample = f.read(1024)
                    f.seek(0)

                    sniffer = csv.Sniffer()
                    delimiter = sniffer.sniff(sample).delimiter

                    reader = csv.reader(f, delimiter=delimiter)
                    for row in reader:
                        if row:  # 跳过空行
                            # 取第一列作为域名数据
                            data.append(row[0].strip())
                break
            except (UnicodeDecodeError, csv.Error):
                continue

        return data

    def _parse_xlsx_file(self, file_path: str) -> List[str]:
        """解析XLSX文件"""
        try:
            df = pd.read_excel(file_path, header=None)
            # 取第一列数据
            data = df.iloc[:, 0].dropna().astype(str).tolist()
            return [item.strip() for item in data if item.strip()]
        except Exception as e:
            raise Exception(f"Excel文件解析失败: {str(e)}")

    def _parse_json_file(self, file_path: str) -> List[str]:
        """解析JSON文件"""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            data = json.load(f)

        result = []

        if isinstance(data, list):
            # 如果是数组，直接处理每个元素
            for item in data:
                if isinstance(item, str):
                    result.append(item.strip())
                elif isinstance(item, dict):
                    # 如果是对象，尝试找到域名字段
                    for key in ['domain', 'url', 'website', 'site']:
                        if key in item and isinstance(item[key], str):
                            result.append(item[key].strip())
                            break
        elif isinstance(data, dict):
            # 如果是对象，尝试找到包含域名的字段
            for key, value in data.items():
                if isinstance(value, list):
                    for item in value:
                        if isinstance(item, str):
                            result.append(item.strip())
                elif isinstance(value, str):
                    result.append(value.strip())

        return result

    def _process_raw_data(self, raw_data: List[str]) -> Dict[str, Union[List[Dict], int]]:
        """
        处理原始数据，提取和验证域名

        Args:
            raw_data: 原始数据列表

        Returns:
            处理结果字典
        """
        domains = []
        seen_domains = set()
        total_count = len(raw_data)
        valid_count = 0
        duplicate_count = 0

        for line in raw_data:
            if not line:
                continue

            # 解析域名和描述
            domain_info = self._parse_domain_line(line)

            if domain_info['domain']:
                # 提取根域名
                root_domain = self._extract_root_domain(domain_info['domain'])

                if root_domain and self._is_valid_domain(root_domain):
                    root_domain = root_domain.lower()

                    if root_domain in seen_domains:
                        duplicate_count += 1
                    else:
                        seen_domains.add(root_domain)
                        domains.append({
                            'domain': root_domain,
                            'description': domain_info['description'],
                            'original': line.strip()
                        })
                        valid_count += 1

        return {
            'domains': domains,
            'total': total_count,
            'valid': valid_count,
            'duplicates': duplicate_count
        }

    def _parse_domain_line(self, line: str) -> Dict[str, str]:
        """
        解析单行域名数据

        Args:
            line: 单行数据

        Returns:
            包含域名和描述的字典
        """
        line = line.strip()
        domain = ""
        description = "用户录入"

        # 尝试不同的分隔符
        if ',' in line:
            # 域名,备注 格式
            parts = line.split(',', 1)
            domain = parts[0].strip()
            if len(parts) > 1 and parts[1].strip():
                description = parts[1].strip()
        elif '-' in line and not line.startswith('-') and not line.endswith('-'):
            # 域名-备注 格式 (排除以-开头或结尾的情况)
            parts = line.split('-', 1)
            domain = parts[0].strip()
            if len(parts) > 1 and parts[1].strip():
                description = parts[1].strip()
        elif ' ' in line:
            # 域名 备注 格式
            parts = line.split(' ', 1)
            domain = parts[0].strip()
            if len(parts) > 1 and parts[1].strip():
                description = parts[1].strip()
        else:
            # 只有域名
            domain = line.strip()

        return {
            'domain': domain,
            'description': description
        }

    def _extract_root_domain(self, input_text: str) -> Optional[str]:
        """
        从URL或域名文本中提取根域名

        Args:
            input_text: 输入文本

        Returns:
            根域名或None
        """
        if not input_text:
            return None

        # 清理输入文本
        input_text = input_text.strip().rstrip('/')
        domain = None

        # 检查是否是URL格式
        if input_text.startswith(('http://', 'https://', 'ftp://', 'ftps://')):
            try:
                parsed = urlparse(input_text)
                domain = parsed.netloc

                # 移除端口号
                if ':' in domain:
                    domain = domain.split(':')[0]
            except Exception:
                domain = self._extract_domain_with_regex(input_text)
        elif input_text.startswith('www.'):
            # 处理以www开头但没有协议的情况
            domain = input_text
        else:
            # 可能是纯域名，但也要检查是否包含路径
            if '/' in input_text:
                domain = input_text.split('/')[0]
            else:
                domain = input_text

        # 如果还没有提取到域名，尝试正则提取
        if not domain:
            domain = self._extract_domain_with_regex(input_text)

        # 提取根域名
        if domain:
            return self._get_root_domain(domain)

        return None

    def _extract_domain_with_regex(self, text: str) -> Optional[str]:
        """使用正则表达式从复杂文本中提取域名"""
        patterns = [
            # 匹配完整URL中的域名部分
            r'(?:https?://)?(?:www\.)?([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)+)(?:/.*)?',
            # 匹配纯域名
            r'^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)+)$',
            # 匹配带www的域名
            r'^(?:www\.)?([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)+)',
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                domain = match.group(1)
                if domain and '.' in domain:
                    return domain

        return text if '.' in text else None

    def _get_root_domain(self, domain: str) -> str:
        """提取根域名（去除子域名前缀）"""
        if not domain:
            return domain

        # 移除www前缀
        if domain.startswith('www.'):
            domain = domain[4:]

        # 分割域名部分
        parts = domain.split('.')

        # 如果只有两个部分，直接返回
        if len(parts) <= 2:
            return domain

        # 检查是否是特殊的二级域名
        if len(parts) >= 3:
            last_two = '.'.join(parts[-2:])
            if last_two in self.common_slds:
                return '.'.join(parts[-3:]) if len(parts) >= 3 else domain

        # 默认返回最后两个部分作为根域名
        return '.'.join(parts[-2:])

    def _is_valid_domain(self, domain: str) -> bool:
        """验证域名格式是否有效"""
        if not domain or not isinstance(domain, str):
            return False

        # 基本格式检查
        if len(domain) > 253 or len(domain) < 4:
            return False

        # 必须包含至少一个点
        if '.' not in domain:
            return False

        # 不能以点开头或结尾
        if domain.startswith('.') or domain.endswith('.'):
            return False

        # 不能包含连续的点
        if '..' in domain:
            return False

        # 使用正则表达式验证
        return bool(self.domain_pattern.match(domain))

    def get_preview_data(self, domains_data: List[Dict]) -> Dict[str, Union[List[Dict], int]]:
        """
        获取预览数据

        Args:
            domains_data: 域名数据列表

        Returns:
            预览数据字典
        """
        if not domains_data:
            return {
                'preview_items': [],
                'total_count': 0,
                'valid_count': 0,
                'duplicate_count': 0
            }

        preview_items = []
        for i, domain_info in enumerate(domains_data[:50]):  # 最多预览50个
            preview_items.append({
                'index': i + 1,
                'domain': domain_info['domain'],
                'description': domain_info['description'],
                'original': domain_info.get('original', domain_info['domain'])
            })

        return {
            'preview_items': preview_items,
            'total_count': len(domains_data),
            'showing_count': len(preview_items),
            'has_more': len(domains_data) > 50
        }