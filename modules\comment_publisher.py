#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CommentHunter - 评论发布模块
功能：文章定位、内容处理、表单填写、批量发布、反检测
"""

import re
import time
import random
import requests
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from fake_useragent import UserAgent
from .proxy_manager import ProxyManager
from typing import Dict, List, Optional, Tuple, Union


class CommentPublisher:
    """评论发布器类"""

    def __init__(self, proxy_config: Optional[Dict] = None, selenium_config: Optional[Dict] = None):
        """
        初始化评论发布器

        Args:
            proxy_config: 代理配置
            selenium_config: Selenium配置
        """
        self.proxy_config = proxy_config
        self.selenium_config = selenium_config or {}
        self.ua = UserAgent()
        self.session = requests.Session()
        self.driver = None

        # 初始化代理管理器
        self.proxy_manager = ProxyManager()

        # 默认不使用代理，由用户手动配置
        self.proxy_enabled = False
        self.session.proxies = {}

    def _test_proxy(self, proxy_url: str) -> bool:
        """测试代理是否可用"""
        try:
            test_session = requests.Session()
            test_session.proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
            response = test_session.get('http://httpbin.org/ip', timeout=5)
            return response.status_code == 200
        except Exception as e:
            print(f"代理测试失败: {e}")
            return False

        # 反检测配置
        self.anti_detection = {
            'min_delay': 2,
            'max_delay': 8,
            'typing_delay': (0.1, 0.3),
            'scroll_probability': 0.7,
            'mouse_move_probability': 0.5
        }

        # 评论模板变量
        self.template_vars = {
            '{site_name}': '',
            '{article_title}': '',
            '{author_name}': '',
            '{current_date}': time.strftime('%Y-%m-%d'),
            '{current_time}': time.strftime('%H:%M:%S'),
            '{random_emoji}': ['👍', '😊', '💯', '🔥', '👏', '❤️', '🎉', '✨']
        }

    def init_selenium_driver(self) -> bool:
        """
        初始化Selenium WebDriver

        Returns:
            是否初始化成功
        """
        try:
            chrome_options = Options()

            # 基础配置
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 用户代理
            chrome_options.add_argument(f'--user-agent={self.ua.random}')

            # 代理设置 - 使用127.0.0.1:7897
            if self.proxy_enabled:
                proxy_url = "127.0.0.1:7897"
                chrome_options.add_argument(f'--proxy-server={proxy_url}')
                print(f"Selenium WebDriver已配置代理: {proxy_url}")
            else:
                print("Selenium WebDriver将使用直连模式")

            # 无头模式（可选）
            if self.selenium_config.get('headless', False):
                chrome_options.add_argument('--headless')

            # 窗口大小
            window_size = self.selenium_config.get('window_size', '1366,768')
            chrome_options.add_argument(f'--window-size={window_size}')

            self.driver = webdriver.Chrome(options=chrome_options)

            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            return True

        except Exception as e:
            print(f"初始化WebDriver失败: {str(e)}")
            return False

    def publish_comment(self, site_info: Dict, comment_data: Dict) -> Dict:
        """
        发布单条评论

        Args:
            site_info: 网站信息
            comment_data: 评论数据

        Returns:
            发布结果
        """
        result = {
            'success': False,
            'site_domain': site_info.get('domain', ''),
            'article_url': comment_data.get('article_url', ''),
            'comment_content': comment_data.get('content', ''),
            'error': None,
            'published_at': None,
            'response_data': None
        }

        try:
            # 1. 初始化WebDriver（如果需要）
            if not self.driver and not self.init_selenium_driver():
                result['error'] = 'WebDriver初始化失败'
                return result

            # 2. 访问文章页面
            article_url = comment_data['article_url']
            self.driver.get(article_url)

            # 3. 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # 4. 模拟人类行为
            self._simulate_human_behavior()

            # 5. 查找评论表单
            comment_form = self._find_comment_form(site_info.get('cms_type', 'generic'))
            if not comment_form:
                result['error'] = '未找到评论表单'
                return result

            # 6. 填写评论表单
            form_data = self._prepare_form_data(comment_data, site_info)
            fill_success = self._fill_comment_form(comment_form, form_data, site_info.get('cms_type', 'generic'))

            if not fill_success:
                result['error'] = '表单填写失败'
                return result

            # 7. 提交评论
            submit_success = self._submit_comment_form(comment_form, site_info.get('cms_type', 'generic'))

            if submit_success:
                result['success'] = True
                result['published_at'] = time.strftime('%Y-%m-%d %H:%M:%S')
                result['response_data'] = self._get_response_info()
            else:
                result['error'] = '评论提交失败'

        except TimeoutException:
            result['error'] = '页面加载超时'
        except Exception as e:
            result['error'] = f'发布过程出错: {str(e)}'

        return result

    def _simulate_human_behavior(self):
        """模拟人类浏览行为"""
        try:
            # 随机滚动页面
            if random.random() < self.anti_detection['scroll_probability']:
                scroll_height = self.driver.execute_script("return document.body.scrollHeight")
                scroll_to = random.randint(100, min(scroll_height, 1000))
                self.driver.execute_script(f"window.scrollTo(0, {scroll_to});")
                time.sleep(random.uniform(1, 2))

            # 随机鼠标移动
            if random.random() < self.anti_detection['mouse_move_probability']:
                from selenium.webdriver.common.action_chains import ActionChains
                actions = ActionChains(self.driver)
                actions.move_by_offset(random.randint(-50, 50), random.randint(-50, 50))
                actions.perform()

            # 随机延迟
            delay = random.uniform(self.anti_detection['min_delay'], self.anti_detection['max_delay'])
            time.sleep(delay)

        except Exception as e:
            print(f"模拟人类行为失败: {str(e)}")

    def _find_comment_form(self, cms_type: str) -> Optional[object]:
        """
        查找评论表单

        Args:
            cms_type: CMS类型

        Returns:
            表单元素或None
        """
        # CMS特定的表单选择器
        cms_selectors = {
            'wordpress': [
                '#respond', '#commentform', '.comment-form',
                'form[action*="wp-comments-post"]'
            ],
            'zblog': [
                '#divCommentPost', '#frmSumbit', '.comment-form'
            ],
            'discuz': [
                '#fastpostform', '.fastpost', '#quickpostform'
            ],
            'generic': [
                'form[action*="comment"]', '.comment-form', '#comment-form',
                'form[id*="comment"]', 'form[class*="comment"]'
            ]
        }

        selectors = cms_selectors.get(cms_type, cms_selectors['generic'])

        for selector in selectors:
            try:
                element = self.driver.find_element(By.CSS_SELECTOR, selector)
                if element.is_displayed():
                    return element
            except NoSuchElementException:
                continue

        return None

    def _prepare_form_data(self, comment_data: Dict, site_info: Dict) -> Dict:
        """
        准备表单数据

        Args:
            comment_data: 评论数据
            site_info: 网站信息

        Returns:
            处理后的表单数据
        """
        # 更新模板变量
        self.template_vars['{site_name}'] = site_info.get('domain', '')
        self.template_vars['{article_title}'] = self._get_article_title()
        self.template_vars['{author_name}'] = comment_data.get('author', '匿名用户')

        # 处理评论内容
        content = comment_data.get('content', '')
        content = self._process_template_variables(content)

        return {
            'author': comment_data.get('author', ''),
            'email': comment_data.get('email', ''),
            'url': comment_data.get('url', ''),
            'content': content
        }

    def _process_template_variables(self, text: str) -> str:
        """
        处理模板变量

        Args:
            text: 原始文本

        Returns:
            处理后的文本
        """
        for var, value in self.template_vars.items():
            if var in text:
                if isinstance(value, list):
                    # 随机选择一个值
                    replacement = random.choice(value)
                else:
                    replacement = str(value)
                text = text.replace(var, replacement)

        return text

    def _get_article_title(self) -> str:
        """获取文章标题"""
        try:
            # 尝试多种标题选择器
            title_selectors = [
                'h1', '.entry-title', '.post-title', '.article-title',
                'title', '.title', '#title'
            ]

            for selector in title_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    title = element.text.strip()
                    if title and len(title) > 5:
                        return title
                except NoSuchElementException:
                    continue

            # 如果都没找到，使用页面标题
            return self.driver.title

        except Exception:
            return '文章'

    def _fill_comment_form(self, form_element: object, form_data: Dict, cms_type: str) -> bool:
        """
        填写评论表单

        Args:
            form_element: 表单元素
            form_data: 表单数据
            cms_type: CMS类型

        Returns:
            是否填写成功
        """
        try:
            # CMS特定的字段映射
            field_mappings = {
                'wordpress': {
                    'author': ['author', 'comment_author', 'name'],
                    'email': ['email', 'comment_author_email', 'mail'],
                    'url': ['url', 'comment_author_url', 'website'],
                    'content': ['comment', 'comment_content', 'message']
                },
                'zblog': {
                    'author': ['inpName', 'tbName', 'name'],
                    'email': ['inpEmail', 'tbMail', 'email'],
                    'url': ['inpHomePage', 'tbHomePage', 'url'],
                    'content': ['txaArticle', 'tbComment', 'comment']
                },
                'discuz': {
                    'author': ['username', 'author', 'name'],
                    'email': ['email', 'mail'],
                    'content': ['message', 'fastpostmessage', 'comment']
                },
                'generic': {
                    'author': ['name', 'author', 'username', 'user'],
                    'email': ['email', 'mail', 'e-mail'],
                    'url': ['url', 'website', 'homepage', 'site'],
                    'content': ['comment', 'message', 'content', 'text', 'body']
                }
            }

            mapping = field_mappings.get(cms_type, field_mappings['generic'])

            # 填写各个字段
            for field_type, field_names in mapping.items():
                if field_type in form_data and form_data[field_type]:
                    success = self._fill_field(form_element, field_names, form_data[field_type])
                    if not success and field_type == 'content':
                        # 评论内容是必填的
                        return False

            return True

        except Exception as e:
            print(f"填写表单失败: {str(e)}")
            return False

    def _fill_field(self, form_element: object, field_names: List[str], value: str) -> bool:
        """
        填写单个字段

        Args:
            form_element: 表单元素
            field_names: 字段名列表
            value: 字段值

        Returns:
            是否填写成功
        """
        for field_name in field_names:
            # 尝试不同的选择器
            selectors = [
                f'input[name="{field_name}"]',
                f'textarea[name="{field_name}"]',
                f'#{field_name}',
                f'.{field_name}'
            ]

            for selector in selectors:
                try:
                    element = form_element.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed() and element.is_enabled():
                        # 清空字段
                        element.clear()

                        # 模拟人类输入
                        self._human_type(element, value)
                        return True

                except NoSuchElementException:
                    continue

        return False

    def _human_type(self, element: object, text: str):
        """
        模拟人类输入

        Args:
            element: 输入元素
            text: 输入文本
        """
        for char in text:
            element.send_keys(char)
            # 随机输入延迟
            delay = random.uniform(*self.anti_detection['typing_delay'])
            time.sleep(delay)

    def _submit_comment_form(self, form_element: object, cms_type: str) -> bool:
        """
        提交评论表单

        Args:
            form_element: 表单元素
            cms_type: CMS类型

        Returns:
            是否提交成功
        """
        try:
            # CMS特定的提交按钮选择器
            submit_selectors = {
                'wordpress': [
                    '#submit', '.submit', 'input[type="submit"]',
                    'button[type="submit"]'
                ],
                'zblog': [
                    '#btnSumbit', '.submit-btn', 'input[type="submit"]'
                ],
                'discuz': [
                    '#fastpostsubmit', '.btn_submit', 'input[type="submit"]'
                ],
                'generic': [
                    'input[type="submit"]', 'button[type="submit"]',
                    '.submit', '.btn-submit', '#submit'
                ]
            }

            selectors = submit_selectors.get(cms_type, submit_selectors['generic'])

            # 查找提交按钮
            submit_button = None
            for selector in selectors:
                try:
                    submit_button = form_element.find_element(By.CSS_SELECTOR, selector)
                    if submit_button.is_displayed() and submit_button.is_enabled():
                        break
                except NoSuchElementException:
                    continue

            if not submit_button:
                print("未找到提交按钮")
                return False

            # 滚动到按钮位置
            self.driver.execute_script("arguments[0].scrollIntoView();", submit_button)
            time.sleep(1)

            # 点击提交按钮
            submit_button.click()

            # 等待提交完成
            time.sleep(3)

            # 检查是否提交成功
            return self._check_submit_success()

        except Exception as e:
            print(f"提交表单失败: {str(e)}")
            return False

    def _check_submit_success(self) -> bool:
        """
        检查提交是否成功

        Returns:
            是否提交成功
        """
        try:
            # 检查成功提示信息
            success_indicators = [
                '评论已提交', '评论成功', '感谢您的评论', 'comment submitted',
                'thank you', '审核中', 'pending approval', '等待审核'
            ]

            page_text = self.driver.page_source.lower()

            for indicator in success_indicators:
                if indicator.lower() in page_text:
                    return True

            # 检查URL是否发生变化（可能跳转到成功页面）
            current_url = self.driver.current_url
            if 'success' in current_url or 'thank' in current_url:
                return True

            # 检查是否有错误信息
            error_indicators = [
                '错误', 'error', '失败', 'failed', '验证码', 'captcha',
                '登录', 'login', '注册', 'register'
            ]

            for indicator in error_indicators:
                if indicator.lower() in page_text:
                    return False

            # 默认认为成功（保守策略）
            return True

        except Exception as e:
            print(f"检查提交状态失败: {str(e)}")
            return False

    def _get_response_info(self) -> Dict:
        """
        获取响应信息

        Returns:
            响应信息字典
        """
        try:
            return {
                'current_url': self.driver.current_url,
                'page_title': self.driver.title,
                'response_time': time.time(),
                'page_source_length': len(self.driver.page_source)
            }
        except Exception:
            return {}

    def batch_publish(self, sites_data: List[Dict], comments_data: List[Dict],
                     max_concurrent: int = 1) -> List[Dict]:
        """
        批量发布评论

        Args:
            sites_data: 网站数据列表
            comments_data: 评论数据列表
            max_concurrent: 最大并发数

        Returns:
            发布结果列表
        """
        results = []

        try:
            for i, site_info in enumerate(sites_data):
                print(f"正在处理网站 {i+1}/{len(sites_data)}: {site_info.get('domain', '')}")

                # 为每个网站发布评论
                for comment_data in comments_data:
                    # 添加网站相关的文章URL
                    if 'article_urls' in site_info and site_info['article_urls']:
                        comment_data['article_url'] = random.choice(site_info['article_urls'])

                        result = self.publish_comment(site_info, comment_data)
                        results.append(result)

                        # 随机延迟，避免被检测
                        delay = random.uniform(5, 15)
                        time.sleep(delay)

                # 网站间延迟
                if i < len(sites_data) - 1:
                    delay = random.uniform(10, 30)
                    print(f"等待 {delay:.1f} 秒后处理下一个网站...")
                    time.sleep(delay)

        finally:
            self.cleanup()

        return results

    def cleanup(self):
        """清理资源"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
        except Exception as e:
            print(f"清理资源失败: {str(e)}")

    def __del__(self):
        """析构函数"""
        self.cleanup()