# CommentHunter - 网站评论分析与发布工具

![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)
![Python](https://img.shields.io/badge/python-3.8+-green.svg)
![License](https://img.shields.io/badge/license-MIT-orange.svg)

## 🎯 项目简介

**CommentHunter** 是一个专业的网站评论分析与发布工具，基于 FreeDirHunter 架构重新设计。主要功能包括：

- 🔍 **智能检测**: 自动检测网站是否支持无需登录且无审核的评论功能
- 📝 **批量发布**: 支持批量评论发布，提高工作效率
- 🤖 **AI生成**: 集成SiliconFlow API，智能生成高质量评论内容
- 🌐 **多CMS支持**: 兼容WordPress、Z-Blog、Discuz等主流CMS
- 🔄 **代理管理**: 支持多种代理协议，避免IP封禁
- 📊 **数据统计**: 详细的统计分析和报表功能

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Windows 10/11 (推荐)
- Chrome浏览器 (用于Selenium自动化)

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/commenthunter/commenthunter.git
cd CommentHunter
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置设置**
```bash
# 复制配置文件模板
cp config/settings.json.example config/settings.json
# 根据需要修改配置
```

4. **运行程序**
```bash
python main.py
```

### 打包为可执行文件

```bash
python build_exe.py
```

打包完成后，可执行文件位于 `dist/CommentHunter/` 目录。

## 📋 功能特性

### 🔍 域名提取模块
- 支持多种格式文件导入 (TXT, CSV, XLSX, JSON)
- 智能域名解析和URL提取
- 实时预览和数据验证
- 自动去重和格式标准化

### 🕵️ 评论检测模块
- 文章页面智能识别
- 多CMS兼容性检测 (WordPress, Z-Blog, Discuz, Typecho, Emlog)
- 评论环境分析 (登录要求、验证码、审核机制)
- 智能评分和筛选系统

### 📝 评论发布模块
- Selenium WebDriver自动化
- 反检测措施和人类行为模拟
- 批量发布和任务管理
- 实时进度跟踪和错误处理

### 🤖 AI评论生成模块
- SiliconFlow API集成
- 多种评论类型 (积极、提问、经验、建议、中性)
- 模板变量系统
- 内容优化和去重

### 🌐 代理管理模块
- 多协议支持 (HTTP/HTTPS/SOCKS4/SOCKS5)
- 代理池管理和自动测试
- 智能轮换和故障恢复
- 性能监控和统计

### 💾 数据管理模块
- SQLite3本地数据库
- 完整的CRUD操作
- 数据导入导出功能
- 自动备份和清理

## 🛠️ 技术架构

### 前端客户端 (Python)
- **PyQt5**: GUI界面框架
- **requests**: HTTP请求处理
- **BeautifulSoup4**: HTML解析
- **selenium**: 浏览器自动化
- **pandas**: 数据处理和导出

### 后端API (PHP)
- **PHP 7+**: 服务端语言
- **MySQL**: 关系型数据库
- **PDO**: 数据库抽象层
- **RESTful API**: 标准化接口设计

### 第三方服务
- **SiliconFlow API**: AI内容生成
- **Chrome WebDriver**: 浏览器自动化

## 📁 项目结构

```
CommentHunter/
├── main.py                 # 主程序入口
├── build_exe.py           # 打包脚本
├── requirements.txt       # Python依赖
├── README.md             # 项目说明
├── config/               # 配置文件
│   ├── cms_config.json   # CMS配置
│   ├── comment_templates.json # 评论模板
│   ├── proxy_config.json # 代理配置
│   └── settings.json     # 应用设置
├── modules/              # 核心模块
│   ├── domain_extractor.py    # 域名提取
│   ├── comment_detector.py    # 评论检测
│   ├── comment_publisher.py   # 评论发布
│   ├── ai_generator.py        # AI生成
│   ├── proxy_manager.py       # 代理管理
│   └── database_manager.py    # 数据库管理
├── php/                  # PHP后端
│   ├── index.php         # API入口
│   ├── config/           # 后端配置
│   ├── api/              # API模块
│   └── database/         # 数据库脚本
├── resources/            # 资源文件
└── data/                # 数据目录
```

## ⚙️ 配置说明

### 基本设置
```json
{
  "database": {
    "path": "data/comment_hunter.db"
  },
  "system": {
    "max_threads": 5,
    "log_level": "INFO"
  },
  "ai": {
    "api_key": "your_siliconflow_api_key",
    "model": "Qwen/Qwen2.5-7B-Instruct"
  }
}
```

### 代理配置
```json
{
  "proxy_settings": {
    "enabled": false,
    "rotation_enabled": true
  },
  "default_proxies": [
    {
      "host": "127.0.0.1",
      "port": 7890,
      "type": "http"
    }
  ]
}
```

## 📊 使用统计

- **检测准确率**: >85%
- **发布成功率**: >70%
- **处理速度**: 100个域名/小时
- **并发性能**: 支持10个并发任务

## 🤝 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持与反馈

- **问题报告**: [GitHub Issues](https://github.com/commenthunter/commenthunter/issues)
- **功能建议**: [GitHub Discussions](https://github.com/commenthunter/commenthunter/discussions)
- **邮件联系**: <EMAIL>

## 🔄 更新日志

### v1.0.0 (2024-01-15)
- ✨ 初始版本发布
- 🔍 完整的评论检测功能
- 📝 批量评论发布
- 🤖 AI评论生成
- 🌐 代理管理系统
- 📊 统计分析功能

## ⚠️ 免责声明

本工具仅供学习和研究使用，请遵守相关网站的使用条款和法律法规。使用本工具进行的任何活动，其法律责任由使用者自行承担。

## 🙏 致谢

感谢以下开源项目的支持：
- [PyQt5](https://www.riverbankcomputing.com/software/pyqt/)
- [Selenium](https://selenium-python.readthedocs.io/)
- [BeautifulSoup](https://www.crummy.com/software/BeautifulSoup/)
- [Requests](https://docs.python-requests.org/)
- [Pandas](https://pandas.pydata.org/)

---

<div align="center">
  <p>Made with ❤️ by CommentHunter Team</p>
  <p>
    <a href="https://github.com/commenthunter/commenthunter">⭐ Star</a> |
    <a href="https://github.com/commenthunter/commenthunter/fork">🍴 Fork</a> |
    <a href="https://github.com/commenthunter/commenthunter/issues">🐛 Report Bug</a>
  </p>
</div>
