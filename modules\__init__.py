# -*- coding: utf-8 -*-
"""
CommentHunter 功能模块包
"""

# 导入所有模块，使其可以直接从包中导入
from .domain_extractor import DomainExtractor
from .comment_detector import CommentDetector
from .comment_publisher import CommentPublisher
from .ai_generator import AICommentGenerator, create_ai_generator
from .proxy_manager import ProxyManager
from .config_manager import ConfigManager

__all__ = [
    'DomainExtractor',
    'CommentDetector', 
    'CommentPublisher',
    'AICommentGenerator',
    'create_ai_generator',
    'ProxyManager',
    'ConfigManager'
]
