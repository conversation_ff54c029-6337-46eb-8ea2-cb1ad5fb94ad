{"proxy_settings": {"enabled": true, "rotation_enabled": true, "test_url": "http://httpbin.org/ip", "test_timeout": 10, "max_retries": 3, "failure_threshold": 5, "health_check_interval": 300}, "default_proxies": [{"id": 1, "host": "127.0.0.1", "port": 7897, "type": "http", "username": null, "password": null, "description": "本地代理 (Clash/V2Ray默认端口)", "enabled": true, "priority": 1}, {"id": 2, "host": "127.0.0.1", "port": 1080, "type": "socks5", "username": null, "password": null, "description": "本地SOCKS5代理", "enabled": false, "priority": 2}, {"id": 3, "host": "127.0.0.1", "port": 8080, "type": "http", "username": null, "password": null, "description": "本地HTTP代理", "enabled": false, "priority": 3}], "proxy_types": {"http": {"name": "HTTP代理", "description": "标准HTTP代理协议", "default_port": 8080, "supports_auth": true, "supports_https": true}, "https": {"name": "HTTPS代理", "description": "加密HTTP代理协议", "default_port": 443, "supports_auth": true, "supports_https": true}, "socks4": {"name": "SOCKS4代理", "description": "SOCKS4代理协议", "default_port": 1080, "supports_auth": false, "supports_https": false}, "socks5": {"name": "SOCKS5代理", "description": "SOCKS5代理协议", "default_port": 1080, "supports_auth": true, "supports_https": true}}, "rotation_strategies": {"round_robin": {"name": "轮询", "description": "按顺序轮流使用代理", "enabled": true}, "random": {"name": "随机", "description": "随机选择可用代理", "enabled": true}, "priority": {"name": "优先级", "description": "按优先级使用代理", "enabled": true}, "least_used": {"name": "最少使用", "description": "优先使用使用次数最少的代理", "enabled": false}}, "user_agents": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0"], "request_settings": {"timeout": 30, "max_redirects": 5, "verify_ssl": true, "allow_redirects": true, "headers": {"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "DNT": "1", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1"}}, "selenium_settings": {"headless": true, "window_size": {"width": 1920, "height": 1080}, "page_load_timeout": 30, "implicit_wait": 10, "chrome_options": ["--no-sandbox", "--disable-dev-shm-usage", "--disable-gpu", "--disable-extensions", "--disable-plugins", "--disable-images", "--disable-javascript", "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"], "proxy_settings": {"enabled": true, "use_system_proxy": false}}, "anti_detection": {"random_delays": {"enabled": true, "min_delay": 1, "max_delay": 5, "page_load_delay": {"min": 2, "max": 8}, "form_fill_delay": {"min": 0.5, "max": 2}}, "human_simulation": {"enabled": true, "mouse_movement": true, "scroll_simulation": true, "typing_simulation": true, "click_simulation": true}, "browser_fingerprint": {"randomize_user_agent": true, "randomize_viewport": true, "disable_webgl": true, "disable_plugins": true, "spoof_timezone": false}}, "rate_limiting": {"enabled": true, "requests_per_minute": 30, "requests_per_hour": 1000, "requests_per_day": 10000, "burst_limit": 5, "cooldown_period": 60}, "logging": {"enabled": true, "log_level": "INFO", "log_requests": true, "log_responses": false, "log_errors": true, "max_log_size": "10MB", "log_rotation": true}, "backup_settings": {"enabled": true, "backup_interval": 3600, "max_backups": 10, "backup_path": "backups/proxy_config"}}