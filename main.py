#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CommentHunter - 主程序界面
功能：PyQt5 GUI界面，6个标签页，集成所有功能模块
"""

import sys
import os
import json
import threading
from datetime import datetime
from typing import Dict, List, Optional

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTextEdit, QLineEdit, QComboBox, QSpinBox, QCheckBox,
    QTableWidget, QTableWidgetItem, QFileDialog, QMessageBox, QProgressBar,
    QGroupBox, QGridLayout, QSplitter, QTreeWidget, QTreeWidgetItem,
    QHeaderView, QFrame, QScrollArea
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QPixmap

# 导入功能模块
# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 从 modules 包导入功能模块
from modules.domain_extractor import DomainExtractor
from modules.comment_detector import CommentDetector
from modules.comment_publisher import CommentPublisher
from modules.ai_generator import AICommentGenerator, create_ai_generator
from modules.proxy_manager import ProxyManager
from modules.config_manager import ConfigManager


class WorkerThread(QThread):
    """工作线程基类"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    result_ready = pyqtSignal(object)
    error_occurred = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.is_running = False

    def stop(self):
        self.is_running = False


class DomainExtractorThread(WorkerThread):
    """域名提取工作线程"""

    def __init__(self, file_path: str, extractor: DomainExtractor):
        super().__init__()
        self.file_path = file_path
        self.extractor = extractor

    def run(self):
        try:
            self.is_running = True
            self.status_updated.emit("正在提取域名...")

            result = self.extractor.extract_from_file(self.file_path)

            if self.is_running:
                self.result_ready.emit(result)
                self.status_updated.emit("域名提取完成")

        except Exception as e:
            self.error_occurred.emit(f"域名提取失败: {str(e)}")


class CommentDetectorThread(WorkerThread):
    """评论检测工作线程"""

    def __init__(self, domains: List[str], detector: CommentDetector):
        super().__init__()
        self.domains = domains
        self.detector = detector

    def run(self):
        try:
            self.is_running = True
            total = len(self.domains)

            for i, domain in enumerate(self.domains):
                if not self.is_running:
                    break

                self.status_updated.emit(f"正在检测 {domain}...")
                result = self.detector.detect_comment_capability(domain)

                progress = int((i + 1) / total * 100)
                self.progress_updated.emit(progress)

                if result['has_comment_capability']:
                    self.result_ready.emit(result)

            if self.is_running:
                self.status_updated.emit("评论检测完成")

        except Exception as e:
            self.error_occurred.emit(f"评论检测失败: {str(e)}")


class CommentHunterMainWindow(QMainWindow):
    """CommentHunter主窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("CommentHunter - 网站评论分析与发布工具")
        self.setGeometry(100, 100, 1200, 800)

        # 初始化组件
        self.init_components()
        self.init_ui()
        self.init_connections()

        # 状态栏
        self.statusBar().showMessage("就绪")

        # 定时器用于更新状态
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_dashboard)
        self.timer.start(30000)  # 30秒更新一次

    def init_components(self):
        """初始化功能组件"""
        try:
            # 配置管理器
            self.config_manager = ConfigManager()

            # 域名提取器
            self.domain_extractor = DomainExtractor()

            # 评论检测器
            self.comment_detector = CommentDetector()

            # 代理管理器
            self.proxy_manager = ProxyManager()

            # AI评论生成器
            self.ai_generator = create_ai_generator()

            # 评论发布器
            self.comment_publisher = CommentPublisher()

            print("功能组件初始化完成")

        except Exception as e:
            QMessageBox.critical(self, "初始化错误", f"组件初始化失败: {str(e)}")

    def init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)

        # 创建标签页
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # 创建各个标签页
        self.create_dashboard_tab()
        self.create_domain_extractor_tab()
        self.create_comment_detector_tab()
        self.create_comment_publisher_tab()
        self.create_ai_generator_tab()
        self.create_settings_tab()

    def create_dashboard_tab(self):
        """创建仪表板标签页"""
        dashboard_widget = QWidget()
        layout = QVBoxLayout(dashboard_widget)

        # 标题
        title_label = QLabel("📊 CommentHunter 仪表板")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 统计信息区域
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.Box)
        stats_layout = QGridLayout(stats_frame)

        # 网站统计
        self.sites_total_label = QLabel("0")
        self.sites_detected_label = QLabel("0")
        self.sites_today_label = QLabel("0")
        self.sites_success_rate_label = QLabel("0%")

        stats_layout.addWidget(QLabel("网站总数:"), 0, 0)
        stats_layout.addWidget(self.sites_total_label, 0, 1)
        stats_layout.addWidget(QLabel("已检测:"), 0, 2)
        stats_layout.addWidget(self.sites_detected_label, 0, 3)
        stats_layout.addWidget(QLabel("今日新增:"), 1, 0)
        stats_layout.addWidget(self.sites_today_label, 1, 1)
        stats_layout.addWidget(QLabel("检测成功率:"), 1, 2)
        stats_layout.addWidget(self.sites_success_rate_label, 1, 3)

        # 评论统计
        self.comments_total_label = QLabel("0")
        self.comments_published_label = QLabel("0")
        self.comments_today_label = QLabel("0")
        self.comments_success_rate_label = QLabel("0%")

        stats_layout.addWidget(QLabel("评论总数:"), 2, 0)
        stats_layout.addWidget(self.comments_total_label, 2, 1)
        stats_layout.addWidget(QLabel("已发布:"), 2, 2)
        stats_layout.addWidget(self.comments_published_label, 2, 3)
        stats_layout.addWidget(QLabel("今日发布:"), 3, 0)
        stats_layout.addWidget(self.comments_today_label, 3, 1)
        stats_layout.addWidget(QLabel("发布成功率:"), 3, 2)
        stats_layout.addWidget(self.comments_success_rate_label, 3, 3)

        layout.addWidget(stats_frame)

        # 最近活动
        activity_group = QGroupBox("最近活动")
        activity_layout = QVBoxLayout(activity_group)

        self.activity_text = QTextEdit()
        self.activity_text.setMaximumHeight(200)
        self.activity_text.setReadOnly(True)
        activity_layout.addWidget(self.activity_text)

        layout.addWidget(activity_group)

        # 快速操作按钮
        quick_actions_group = QGroupBox("快速操作")
        quick_actions_layout = QHBoxLayout(quick_actions_group)

        self.refresh_dashboard_btn = QPushButton("刷新仪表板")
        self.backup_database_btn = QPushButton("备份数据库")
        self.cleanup_data_btn = QPushButton("清理旧数据")

        quick_actions_layout.addWidget(self.refresh_dashboard_btn)
        quick_actions_layout.addWidget(self.backup_database_btn)
        quick_actions_layout.addWidget(self.cleanup_data_btn)

        layout.addWidget(quick_actions_group)

        # 添加到标签页
        self.tab_widget.addTab(dashboard_widget, "仪表板")

    def init_connections(self):
        """初始化信号连接"""
        # 仪表板按钮连接
        self.refresh_dashboard_btn.clicked.connect(self.update_dashboard)
        self.backup_database_btn.clicked.connect(self.backup_database)
        self.cleanup_data_btn.clicked.connect(self.cleanup_old_data)

    def create_domain_extractor_tab(self):
        """创建域名提取标签页"""
        extractor_widget = QWidget()
        layout = QVBoxLayout(extractor_widget)

        # 标题
        title_label = QLabel("🔍 域名提取器")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 输入方式选择
        input_method_group = QGroupBox("输入方式")
        input_method_layout = QVBoxLayout(input_method_group)

        # 文件选择区域
        file_section = QWidget()
        file_section_layout = QVBoxLayout(file_section)

        file_header = QLabel("📁 从文件导入")
        file_header.setFont(QFont("Arial", 12, QFont.Bold))
        file_section_layout.addWidget(file_header)

        file_input_layout = QHBoxLayout()
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("选择包含域名的文件 (支持 TXT, CSV, XLSX, JSON)")
        self.browse_file_btn = QPushButton("浏览文件")

        file_input_layout.addWidget(self.file_path_edit)
        file_input_layout.addWidget(self.browse_file_btn)
        file_section_layout.addLayout(file_input_layout)

        input_method_layout.addWidget(file_section)

        # 手动输入区域
        manual_section = QWidget()
        manual_section_layout = QVBoxLayout(manual_section)

        manual_header = QLabel("✏️ 手动输入域名")
        manual_header.setFont(QFont("Arial", 12, QFont.Bold))
        manual_section_layout.addWidget(manual_header)

        self.manual_domains_text = QTextEdit()
        self.manual_domains_text.setPlaceholderText(
            "请输入域名列表，每行一个域名。支持以下格式：\n"
            "• example.com\n"
            "• https://www.example.com\n"
            "• example.com,网站描述\n"
            "• example.com - 网站描述"
        )
        self.manual_domains_text.setMaximumHeight(120)
        manual_section_layout.addWidget(self.manual_domains_text)

        input_method_layout.addWidget(manual_section)
        layout.addWidget(input_method_group)

        # 提取选项
        options_group = QGroupBox("提取选项")
        options_layout = QGridLayout(options_group)

        self.extract_urls_check = QCheckBox("提取完整URL")
        self.validate_domains_check = QCheckBox("验证域名有效性")
        self.remove_duplicates_check = QCheckBox("去除重复域名")

        self.extract_urls_check.setChecked(True)
        self.validate_domains_check.setChecked(True)
        self.remove_duplicates_check.setChecked(True)

        options_layout.addWidget(self.extract_urls_check, 0, 0)
        options_layout.addWidget(self.validate_domains_check, 0, 1)
        options_layout.addWidget(self.remove_duplicates_check, 0, 2)

        layout.addWidget(options_group)

        # 操作按钮
        extract_buttons_layout = QHBoxLayout()

        self.extract_from_file_btn = QPushButton("从文件提取")
        self.extract_from_text_btn = QPushButton("从文本提取")
        self.save_to_ini_btn = QPushButton("保存到数据库")
        self.export_domains_btn = QPushButton("导出文件")
        self.clear_domains_btn = QPushButton("清空结果")

        extract_buttons_layout.addWidget(self.extract_from_file_btn)
        extract_buttons_layout.addWidget(self.extract_from_text_btn)
        extract_buttons_layout.addWidget(self.save_to_ini_btn)
        extract_buttons_layout.addWidget(self.export_domains_btn)
        extract_buttons_layout.addWidget(self.clear_domains_btn)

        layout.addLayout(extract_buttons_layout)

        # 进度条
        self.extract_progress = QProgressBar()
        self.extract_progress.setVisible(False)
        layout.addWidget(self.extract_progress)

        # 结果显示
        results_group = QGroupBox("提取结果")
        results_layout = QVBoxLayout(results_group)

        # 统计信息
        self.extract_stats_label = QLabel("等待提取...")
        results_layout.addWidget(self.extract_stats_label)

        # 结果表格
        self.domains_table = QTableWidget()
        self.domains_table.setColumnCount(4)
        self.domains_table.setHorizontalHeaderLabels(["域名", "类型", "状态", "备注"])
        self.domains_table.horizontalHeader().setStretchLastSection(True)
        results_layout.addWidget(self.domains_table)

        layout.addWidget(results_group)

        # 连接信号
        self.browse_file_btn.clicked.connect(self.browse_domain_file)
        self.extract_from_file_btn.clicked.connect(self.extract_domains_from_file)
        self.extract_from_text_btn.clicked.connect(self.extract_domains_from_text)
        self.save_to_ini_btn.clicked.connect(self.save_domains_to_ini)
        self.export_domains_btn.clicked.connect(self.export_extracted_domains)
        self.clear_domains_btn.clicked.connect(self.clear_domain_results)

        # 添加到标签页
        self.tab_widget.addTab(extractor_widget, "域名提取")

    def create_comment_detector_tab(self):
        """创建评论检测标签页"""
        detector_widget = QWidget()
        layout = QVBoxLayout(detector_widget)

        # 标题
        title_label = QLabel("🔎 评论检测器")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 检测配置
        config_group = QGroupBox("检测配置")
        config_layout = QGridLayout(config_group)

        config_layout.addWidget(QLabel("最大检测页面数:"), 0, 0)
        self.max_pages_spin = QSpinBox()
        self.max_pages_spin.setRange(1, 10)
        self.max_pages_spin.setValue(3)
        config_layout.addWidget(self.max_pages_spin, 0, 1)

        config_layout.addWidget(QLabel("请求超时(秒):"), 0, 2)
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5, 60)
        self.timeout_spin.setValue(10)
        config_layout.addWidget(self.timeout_spin, 0, 3)

        self.use_proxy_check = QCheckBox("使用代理")
        self.save_to_db_check = QCheckBox("保存到数据库")
        self.save_to_db_check.setChecked(True)

        config_layout.addWidget(self.use_proxy_check, 1, 0)
        config_layout.addWidget(self.save_to_db_check, 1, 1)

        layout.addWidget(config_group)

        # 域名输入
        input_group = QGroupBox("域名输入")
        input_layout = QVBoxLayout(input_group)

        input_buttons_layout = QHBoxLayout()
        self.load_domains_btn = QPushButton("从数据库加载")
        self.import_domains_btn = QPushButton("导入域名文件")

        input_buttons_layout.addWidget(self.load_domains_btn)
        input_buttons_layout.addWidget(self.import_domains_btn)
        input_layout.addLayout(input_buttons_layout)

        self.domains_input_text = QTextEdit()
        self.domains_input_text.setPlaceholderText("每行一个域名，或点击上方按钮导入...")
        self.domains_input_text.setMaximumHeight(150)
        input_layout.addWidget(self.domains_input_text)

        layout.addWidget(input_group)

        # 操作按钮
        detect_buttons_layout = QHBoxLayout()

        self.start_detection_btn = QPushButton("开始检测")
        self.stop_detection_btn = QPushButton("停止检测")
        self.export_results_btn = QPushButton("导出结果")

        self.stop_detection_btn.setEnabled(False)

        detect_buttons_layout.addWidget(self.start_detection_btn)
        detect_buttons_layout.addWidget(self.stop_detection_btn)
        detect_buttons_layout.addWidget(self.export_results_btn)

        layout.addLayout(detect_buttons_layout)

        # 进度显示
        self.detect_progress = QProgressBar()
        self.detect_status_label = QLabel("等待开始检测...")

        layout.addWidget(self.detect_progress)
        layout.addWidget(self.detect_status_label)

        # 结果表格
        results_group = QGroupBox("检测结果")
        results_layout = QVBoxLayout(results_group)

        self.detection_results_table = QTableWidget()
        self.detection_results_table.setColumnCount(6)
        self.detection_results_table.setHorizontalHeaderLabels([
            "域名", "CMS类型", "评分", "文章数", "表单信息", "状态"
        ])
        self.detection_results_table.horizontalHeader().setStretchLastSection(True)
        results_layout.addWidget(self.detection_results_table)

        layout.addWidget(results_group)

        # 连接信号
        self.load_domains_btn.clicked.connect(self.load_domains_from_db)
        self.import_domains_btn.clicked.connect(self.import_domains_for_detection)
        self.start_detection_btn.clicked.connect(self.start_comment_detection)
        self.stop_detection_btn.clicked.connect(self.stop_comment_detection)
        self.export_results_btn.clicked.connect(self.export_detection_results)

        # 添加到标签页
        self.tab_widget.addTab(detector_widget, "评论检测")

    def create_comment_publisher_tab(self):
        """创建评论发布标签页"""
        publisher_widget = QWidget()
        layout = QVBoxLayout(publisher_widget)

        # 标题
        title_label = QLabel("📝 评论发布器")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 发布配置
        config_group = QGroupBox("发布配置")
        config_layout = QGridLayout(config_group)

        config_layout.addWidget(QLabel("作者姓名:"), 0, 0)
        self.author_name_edit = QLineEdit()
        self.author_name_edit.setPlaceholderText("评论作者姓名")
        config_layout.addWidget(self.author_name_edit, 0, 1)

        config_layout.addWidget(QLabel("作者邮箱:"), 0, 2)
        self.author_email_edit = QLineEdit()
        self.author_email_edit.setPlaceholderText("评论作者邮箱")
        config_layout.addWidget(self.author_email_edit, 0, 3)

        config_layout.addWidget(QLabel("作者网站:"), 1, 0)
        self.author_website_edit = QLineEdit()
        self.author_website_edit.setPlaceholderText("作者网站URL (可选)")
        config_layout.addWidget(self.author_website_edit, 1, 1)

        config_layout.addWidget(QLabel("发布间隔(秒):"), 1, 2)
        self.publish_interval_spin = QSpinBox()
        self.publish_interval_spin.setRange(5, 300)
        self.publish_interval_spin.setValue(30)
        config_layout.addWidget(self.publish_interval_spin, 1, 3)

        self.use_proxy_publish_check = QCheckBox("使用代理发布")
        self.random_delay_check = QCheckBox("随机延迟")
        self.simulate_human_check = QCheckBox("模拟人类行为")

        self.random_delay_check.setChecked(True)
        self.simulate_human_check.setChecked(True)

        config_layout.addWidget(self.use_proxy_publish_check, 2, 0)
        config_layout.addWidget(self.random_delay_check, 2, 1)
        config_layout.addWidget(self.simulate_human_check, 2, 2)

        layout.addWidget(config_group)

        # 评论内容
        content_group = QGroupBox("评论内容")
        content_layout = QVBoxLayout(content_group)

        content_buttons_layout = QHBoxLayout()
        self.load_templates_btn = QPushButton("加载模板")
        self.generate_ai_comments_btn = QPushButton("AI生成评论")
        self.preview_comments_btn = QPushButton("预览评论")

        content_buttons_layout.addWidget(self.load_templates_btn)
        content_buttons_layout.addWidget(self.generate_ai_comments_btn)
        content_buttons_layout.addWidget(self.preview_comments_btn)
        content_layout.addLayout(content_buttons_layout)

        self.comments_content_text = QTextEdit()
        self.comments_content_text.setPlaceholderText("输入评论内容，每行一条评论...")
        self.comments_content_text.setMaximumHeight(150)
        content_layout.addWidget(self.comments_content_text)

        layout.addWidget(content_group)

        # 目标网站
        targets_group = QGroupBox("目标网站")
        targets_layout = QVBoxLayout(targets_group)

        targets_buttons_layout = QHBoxLayout()
        self.load_detected_sites_btn = QPushButton("加载已检测网站")
        self.import_target_sites_btn = QPushButton("导入目标网站")

        targets_buttons_layout.addWidget(self.load_detected_sites_btn)
        targets_buttons_layout.addWidget(self.import_target_sites_btn)
        targets_layout.addLayout(targets_buttons_layout)

        self.target_sites_table = QTableWidget()
        self.target_sites_table.setColumnCount(5)
        self.target_sites_table.setHorizontalHeaderLabels([
            "域名", "CMS类型", "文章URL", "状态", "操作"
        ])
        self.target_sites_table.horizontalHeader().setStretchLastSection(True)
        self.target_sites_table.setMaximumHeight(200)
        targets_layout.addWidget(self.target_sites_table)

        layout.addWidget(targets_group)

        # 发布控制
        publish_buttons_layout = QHBoxLayout()

        self.start_publishing_btn = QPushButton("开始发布")
        self.pause_publishing_btn = QPushButton("暂停发布")
        self.stop_publishing_btn = QPushButton("停止发布")

        self.pause_publishing_btn.setEnabled(False)
        self.stop_publishing_btn.setEnabled(False)

        publish_buttons_layout.addWidget(self.start_publishing_btn)
        publish_buttons_layout.addWidget(self.pause_publishing_btn)
        publish_buttons_layout.addWidget(self.stop_publishing_btn)

        layout.addLayout(publish_buttons_layout)

        # 发布进度
        self.publish_progress = QProgressBar()
        self.publish_status_label = QLabel("等待开始发布...")

        layout.addWidget(self.publish_progress)
        layout.addWidget(self.publish_status_label)

        # 连接信号
        self.load_templates_btn.clicked.connect(self.load_comment_templates)
        self.generate_ai_comments_btn.clicked.connect(self.generate_ai_comments)
        self.load_detected_sites_btn.clicked.connect(self.load_detected_sites)
        self.start_publishing_btn.clicked.connect(self.start_comment_publishing)
        self.pause_publishing_btn.clicked.connect(self.pause_comment_publishing)
        self.stop_publishing_btn.clicked.connect(self.stop_comment_publishing)

        # 添加到标签页
        self.tab_widget.addTab(publisher_widget, "评论发布")

    def create_ai_generator_tab(self):
        """创建AI生成标签页"""
        ai_widget = QWidget()
        layout = QVBoxLayout(ai_widget)

        # 标题
        title_label = QLabel("🤖 AI评论生成器")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # API配置
        api_group = QGroupBox("API配置")
        api_layout = QGridLayout(api_group)

        api_layout.addWidget(QLabel("API密钥:"), 0, 0)
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setPlaceholderText("输入SiliconFlow API密钥")
        self.api_key_edit.setEchoMode(QLineEdit.Password)
        api_layout.addWidget(self.api_key_edit, 0, 1, 1, 2)

        self.test_api_btn = QPushButton("测试API")
        api_layout.addWidget(self.test_api_btn, 0, 3)

        api_layout.addWidget(QLabel("模型选择:"), 1, 0)
        self.model_combo = QComboBox()
        self.model_combo.addItems([
            "Qwen/Qwen2.5-7B-Instruct",
            "THUDM/glm-4-9b-chat",
            "01-ai/Yi-1.5-9B-Chat-16K"
        ])
        api_layout.addWidget(self.model_combo, 1, 1)

        api_layout.addWidget(QLabel("生成数量:"), 1, 2)
        self.generate_count_spin = QSpinBox()
        self.generate_count_spin.setRange(1, 50)
        self.generate_count_spin.setValue(10)
        api_layout.addWidget(self.generate_count_spin, 1, 3)

        layout.addWidget(api_group)

        # 生成配置
        gen_config_group = QGroupBox("生成配置")
        gen_config_layout = QGridLayout(gen_config_group)

        gen_config_layout.addWidget(QLabel("评论类型:"), 0, 0)
        self.comment_type_combo = QComboBox()
        self.comment_type_combo.addItems([
            "positive", "neutral", "question", "suggestion", "experience"
        ])
        gen_config_layout.addWidget(self.comment_type_combo, 0, 1)

        gen_config_layout.addWidget(QLabel("评论长度:"), 0, 2)
        self.comment_length_combo = QComboBox()
        self.comment_length_combo.addItems(["short", "medium", "long"])
        self.comment_length_combo.setCurrentText("medium")
        gen_config_layout.addWidget(self.comment_length_combo, 0, 3)

        gen_config_layout.addWidget(QLabel("关键词:"), 1, 0)
        self.keywords_edit = QLineEdit()
        self.keywords_edit.setPlaceholderText("用逗号分隔多个关键词")
        gen_config_layout.addWidget(self.keywords_edit, 1, 1, 1, 3)

        layout.addWidget(gen_config_group)

        # 生成按钮
        generate_buttons_layout = QHBoxLayout()

        self.generate_comments_btn = QPushButton("生成评论")
        self.generate_universal_btn = QPushButton("生成10个通用评论")
        self.save_templates_btn = QPushButton("保存为模板")
        self.clear_generated_btn = QPushButton("清空结果")

        generate_buttons_layout.addWidget(self.generate_comments_btn)
        generate_buttons_layout.addWidget(self.generate_universal_btn)
        generate_buttons_layout.addWidget(self.save_templates_btn)
        generate_buttons_layout.addWidget(self.clear_generated_btn)

        layout.addLayout(generate_buttons_layout)

        # 生成结果
        results_group = QGroupBox("生成结果")
        results_layout = QVBoxLayout(results_group)

        self.generated_comments_text = QTextEdit()
        self.generated_comments_text.setPlaceholderText("生成的评论将显示在这里...")
        results_layout.addWidget(self.generated_comments_text)

        layout.addWidget(results_group)

        # 连接信号
        self.test_api_btn.clicked.connect(self.test_ai_api)
        self.generate_comments_btn.clicked.connect(self.generate_ai_comments_batch)
        self.generate_universal_btn.clicked.connect(self.generate_universal_comments)
        self.save_templates_btn.clicked.connect(self.save_generated_templates)
        self.clear_generated_btn.clicked.connect(self.clear_generated_comments)

        # 添加到标签页
        self.tab_widget.addTab(ai_widget, "AI生成")

    def create_settings_tab(self):
        """创建设置标签页"""
        settings_widget = QWidget()
        layout = QVBoxLayout(settings_widget)

        # 标题
        title_label = QLabel("⚙️ 系统设置")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 代理设置
        proxy_group = QGroupBox("代理设置")
        proxy_layout = QVBoxLayout(proxy_group)

        proxy_buttons_layout = QHBoxLayout()
        self.add_proxy_btn = QPushButton("添加代理")
        self.test_proxies_btn = QPushButton("测试代理")
        self.clear_proxies_btn = QPushButton("清空代理")

        proxy_buttons_layout.addWidget(self.add_proxy_btn)
        proxy_buttons_layout.addWidget(self.test_proxies_btn)
        proxy_buttons_layout.addWidget(self.clear_proxies_btn)
        proxy_layout.addLayout(proxy_buttons_layout)

        self.proxy_table = QTableWidget()
        self.proxy_table.setColumnCount(5)
        self.proxy_table.setHorizontalHeaderLabels([
            "代理地址", "端口", "类型", "状态", "延迟"
        ])
        self.proxy_table.horizontalHeader().setStretchLastSection(True)
        self.proxy_table.setMaximumHeight(200)
        proxy_layout.addWidget(self.proxy_table)

        layout.addWidget(proxy_group)

        # 数据库设置
        db_group = QGroupBox("数据库设置")
        db_layout = QGridLayout(db_group)

        db_layout.addWidget(QLabel("数据库路径:"), 0, 0)
        self.db_path_edit = QLineEdit()
        self.db_path_edit.setText("data/comment_hunter.db")
        db_layout.addWidget(self.db_path_edit, 0, 1)

        self.browse_db_btn = QPushButton("浏览")
        db_layout.addWidget(self.browse_db_btn, 0, 2)

        db_buttons_layout = QHBoxLayout()
        self.backup_db_btn = QPushButton("备份数据库")
        self.restore_db_btn = QPushButton("恢复数据库")
        self.optimize_db_btn = QPushButton("优化数据库")

        db_buttons_layout.addWidget(self.backup_db_btn)
        db_buttons_layout.addWidget(self.restore_db_btn)
        db_buttons_layout.addWidget(self.optimize_db_btn)

        db_layout.addLayout(db_buttons_layout, 1, 0, 1, 3)

        layout.addWidget(db_group)

        # 系统设置
        system_group = QGroupBox("系统设置")
        system_layout = QGridLayout(system_group)

        system_layout.addWidget(QLabel("日志级别:"), 0, 0)
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level_combo.setCurrentText("INFO")
        system_layout.addWidget(self.log_level_combo, 0, 1)

        system_layout.addWidget(QLabel("最大线程数:"), 0, 2)
        self.max_threads_spin = QSpinBox()
        self.max_threads_spin.setRange(1, 20)
        self.max_threads_spin.setValue(5)
        system_layout.addWidget(self.max_threads_spin, 0, 3)

        self.auto_backup_check = QCheckBox("自动备份数据库")
        self.auto_cleanup_check = QCheckBox("自动清理旧数据")

        system_layout.addWidget(self.auto_backup_check, 1, 0)
        system_layout.addWidget(self.auto_cleanup_check, 1, 1)

        layout.addWidget(system_group)

        # 保存设置按钮
        save_settings_layout = QHBoxLayout()
        self.save_settings_btn = QPushButton("保存设置")
        self.reset_settings_btn = QPushButton("重置设置")

        save_settings_layout.addWidget(self.save_settings_btn)
        save_settings_layout.addWidget(self.reset_settings_btn)

        layout.addLayout(save_settings_layout)

        # 连接信号
        self.add_proxy_btn.clicked.connect(self.add_proxy_dialog)
        self.test_proxies_btn.clicked.connect(self.test_all_proxies)
        self.clear_proxies_btn.clicked.connect(self.clear_all_proxies)
        self.save_settings_btn.clicked.connect(self.save_application_settings)
        self.reset_settings_btn.clicked.connect(self.reset_application_settings)

        # 添加到标签页
        self.tab_widget.addTab(settings_widget, "设置")

    # 仪表板功能方法
    def update_dashboard(self):
        """更新仪表板数据"""
        try:
            stats = self.config_manager.get_statistics()

            # 更新网站统计
            self.sites_total_label.setText(str(stats.get('valid_sites_count', 0)))
            self.sites_detected_label.setText(str(stats.get('keyword_sites_count', 0)))
            self.sites_today_label.setText("0")  # INI文件暂不支持按日期统计
            self.sites_success_rate_label.setText("100%")  # 简化显示

            # 更新评论统计
            self.comments_total_label.setText(str(stats.get('comments_count', 0)))
            self.comments_published_label.setText("0")  # 发布统计需要单独实现
            self.comments_today_label.setText("0")  # 简化显示
            self.comments_success_rate_label.setText("100%")  # 简化显示

            # 更新活动日志
            self.update_activity_log()

            self.statusBar().showMessage("仪表板已更新")

        except Exception as e:
            QMessageBox.warning(self, "更新失败", f"更新仪表板失败: {str(e)}")

    def update_activity_log(self):
        """更新活动日志"""
        try:
            # 获取最近的活动记录（简化版本）
            activity_text = ""

            # 显示有效网站数量
            valid_sites = self.config_manager.get_valid_sites()
            if valid_sites:
                activity_text += f"[系统] 已检测到 {len(valid_sites)} 个有效网站\n"

            # 显示关键词网站数量
            keyword_sites = self.config_manager.get_keyword_sites()
            if keyword_sites:
                activity_text += f"[系统] 已添加 {len(keyword_sites)} 个关键词网站\n"

            # 显示评论模板数量
            comments = self.config_manager.get_comments()
            if comments:
                activity_text += f"[系统] 已生成 {len(comments)} 个评论模板\n"

            if not activity_text:
                activity_text = "[系统] 暂无活动记录\n"

            self.activity_text.setPlainText(activity_text)

        except Exception as e:
            print(f"更新活动日志失败: {str(e)}")

    def backup_database(self):
        """备份INI配置文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = f"backup/comment_hunter_backup_{timestamp}"

            os.makedirs(backup_dir, exist_ok=True)

            # 备份所有INI文件
            import shutil
            config_files = [
                "config/valid_sites.ini",
                "config/keyword_sites.ini",
                "config/comments.ini"
            ]

            backup_count = 0
            for config_file in config_files:
                if os.path.exists(config_file):
                    shutil.copy2(config_file, backup_dir)
                    backup_count += 1

            if backup_count > 0:
                QMessageBox.information(self, "备份成功", f"已备份 {backup_count} 个配置文件到: {backup_dir}")
            else:
                QMessageBox.warning(self, "备份失败", "没有找到可备份的配置文件")

        except Exception as e:
            QMessageBox.critical(self, "备份错误", f"备份过程中发生错误: {str(e)}")

    def cleanup_old_data(self):
        """清理所有数据"""
        reply = QMessageBox.question(
            self, "确认清理",
            "确定要清理所有本地数据吗？此操作不可撤销。",
            QMessageBox.Ok | QMessageBox.Cancel
        )

        if reply == QMessageBox.Ok:
            try:
                # 清理所有INI文件
                config_files = [
                    "config/valid_sites.ini",
                    "config/keyword_sites.ini",
                    "config/comments.ini"
                ]

                cleared_count = 0
                for config_file in config_files:
                    if os.path.exists(config_file):
                        os.remove(config_file)
                        cleared_count += 1

                if cleared_count > 0:
                    QMessageBox.information(self, "清理完成", f"已清理 {cleared_count} 个配置文件")
                    self.update_dashboard()
                else:
                    QMessageBox.information(self, "清理完成", "没有找到需要清理的文件")

            except Exception as e:
                QMessageBox.critical(self, "清理错误", f"清理过程中发生错误: {str(e)}")

    # 域名提取功能方法
    def browse_domain_file(self):
        """浏览域名文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择域名文件", "",
            "所有支持格式 (*.txt *.csv *.xlsx *.json);;文本文件 (*.txt);;CSV文件 (*.csv);;Excel文件 (*.xlsx);;JSON文件 (*.json)"
        )

        if file_path:
            self.file_path_edit.setText(file_path)

    def extract_domains_from_file(self):
        """从文件提取域名"""
        file_path = self.file_path_edit.text().strip()
        if not file_path:
            QMessageBox.warning(self, "文件错误", "请先选择要提取的文件")
            return

        if not os.path.exists(file_path):
            QMessageBox.warning(self, "文件错误", "选择的文件不存在")
            return

        # 显示进度条
        self.extract_progress.setVisible(True)
        self.extract_progress.setRange(0, 0)  # 不确定进度

        # 创建工作线程
        self.extract_thread = DomainExtractorThread(file_path, self.domain_extractor)
        self.extract_thread.result_ready.connect(self.on_domains_extracted)
        self.extract_thread.status_updated.connect(self.statusBar().showMessage)
        self.extract_thread.error_occurred.connect(self.on_extract_error)
        self.extract_thread.start()

        self.extract_from_file_btn.setEnabled(False)

    def extract_domains_from_text(self):
        """从文本提取域名"""
        text_content = self.manual_domains_text.toPlainText().strip()
        if not text_content:
            QMessageBox.warning(self, "输入错误", "请输入要提取的域名列表")
            return

        try:
            # 直接使用域名提取器处理文本
            result = self.domain_extractor.extract_from_text(text_content)

            if result.get('success'):
                self.on_domains_extracted(result)
                self.statusBar().showMessage("文本域名提取完成")
            else:
                QMessageBox.critical(self, "提取错误", result.get('error', '未知错误'))

        except Exception as e:
            QMessageBox.critical(self, "提取错误", f"文本提取失败: {str(e)}")

    def on_domains_extracted(self, result):
        """域名提取完成"""
        self.extract_progress.setVisible(False)
        self.extract_from_file_btn.setEnabled(True)

        if result and result.get('domains'):
            domains = result['domains']

            # 更新统计信息
            stats_text = f"提取完成: 共 {len(domains)} 个有效域名"
            if result.get('duplicates'):
                stats_text += f", 去重 {result['duplicates']} 个"
            if result.get('total'):
                stats_text += f", 总计处理 {result['total']} 条记录"
            self.extract_stats_label.setText(stats_text)

            # 更新表格
            self.domains_table.setRowCount(len(domains))
            for i, domain_info in enumerate(domains):
                self.domains_table.setItem(i, 0, QTableWidgetItem(domain_info.get('domain', '')))
                self.domains_table.setItem(i, 1, QTableWidgetItem('域名'))
                self.domains_table.setItem(i, 2, QTableWidgetItem('已提取'))
                self.domains_table.setItem(i, 3, QTableWidgetItem(domain_info.get('description', '用户录入')))

            # 存储提取结果供后续使用
            self.extracted_domains = domains
        else:
            QMessageBox.information(self, "提取结果", "未找到有效域名")
            self.extracted_domains = []

    def on_extract_error(self, error_msg):
        """域名提取错误"""
        self.extract_progress.setVisible(False)
        self.extract_from_file_btn.setEnabled(True)
        QMessageBox.critical(self, "提取错误", error_msg)

    def save_domains_to_ini(self):
        """保存域名到数据库(ini文件)"""
        if not hasattr(self, 'extracted_domains') or not self.extracted_domains:
            QMessageBox.warning(self, "保存错误", "没有可保存的域名，请先提取域名")
            return

        try:
            # 使用批量添加方法，自动处理重复域名
            saved_count = self.config_manager.batch_add_extracted_domains(self.extracted_domains)

            if saved_count > 0:
                QMessageBox.information(
                    self, "保存成功",
                    f"已成功保存 {saved_count} 个新域名到数据库\n"
                    f"保存位置: {self.config_manager.valid_sites_file}\n"
                    f"重复域名已自动跳过"
                )
                self.statusBar().showMessage(f"已保存 {saved_count} 个新域名到数据库")
            else:
                QMessageBox.information(
                    self, "保存完成",
                    "所有域名都已存在于数据库中，未添加新域名"
                )
                self.statusBar().showMessage("所有域名都已存在，未添加新域名")

        except Exception as e:
            QMessageBox.critical(self, "保存错误", f"保存到数据库失败: {str(e)}")

    def export_extracted_domains(self):
        """导出提取的域名到文件"""
        if self.domains_table.rowCount() == 0:
            QMessageBox.warning(self, "导出错误", "没有可导出的域名")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出域名", "extracted_domains.txt",
            "文本文件 (*.txt);;CSV文件 (*.csv);;Excel文件 (*.xlsx)"
        )

        if file_path:
            try:
                domains_data = []
                for i in range(self.domains_table.rowCount()):
                    domain = self.domains_table.item(i, 0).text()
                    description = self.domains_table.item(i, 3).text()
                    domains_data.append({
                        'domain': domain,
                        'description': description
                    })

                if file_path.endswith('.csv'):
                    import pandas as pd
                    df = pd.DataFrame(domains_data)
                    df.to_csv(file_path, index=False, encoding='utf-8-sig')
                elif file_path.endswith('.xlsx'):
                    import pandas as pd
                    df = pd.DataFrame(domains_data)
                    df.to_excel(file_path, index=False)
                else:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        for item in domains_data:
                            if item['description'] and item['description'] != '用户录入':
                                f.write(f"{item['domain']},{item['description']}\n")
                            else:
                                f.write(f"{item['domain']}\n")

                QMessageBox.information(self, "导出成功", f"域名已导出到: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "导出错误", f"导出失败: {str(e)}")

    def clear_domain_results(self):
        """清空域名结果"""
        self.domains_table.setRowCount(0)
        self.extract_stats_label.setText("等待提取...")
        self.manual_domains_text.clear()
        self.file_path_edit.clear()
        if hasattr(self, 'extracted_domains'):
            self.extracted_domains = []

    # 评论检测功能方法
    def load_domains_from_db(self):
        """从配置文件加载域名"""
        try:
            sites = self.config_manager.get_valid_sites()
            domains = [site['domain'] for site in sites]

            self.domains_input_text.setPlainText('\n'.join(domains))
            self.statusBar().showMessage(f"已从配置文件加载 {len(domains)} 个域名")

        except Exception as e:
            QMessageBox.critical(self, "加载错误", f"从配置文件加载域名失败: {str(e)}")

    def import_domains_for_detection(self):
        """导入域名用于检测"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择域名文件", "",
            "文本文件 (*.txt);;CSV文件 (*.csv)"
        )

        if file_path:
            try:
                domains = []
                if file_path.endswith('.csv'):
                    import pandas as pd
                    df = pd.read_csv(file_path)
                    if 'domain' in df.columns:
                        domains = df['domain'].tolist()
                    else:
                        domains = df.iloc[:, 0].tolist()
                else:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        domains = [line.strip() for line in f if line.strip()]

                self.domains_input_text.setPlainText('\n'.join(domains))
                self.statusBar().showMessage(f"已导入 {len(domains)} 个域名")

            except Exception as e:
                QMessageBox.critical(self, "导入错误", f"导入域名失败: {str(e)}")

    def start_comment_detection(self):
        """开始评论检测"""
        domains_text = self.domains_input_text.toPlainText().strip()
        if not domains_text:
            QMessageBox.warning(self, "输入错误", "请输入要检测的域名")
            return

        domains = [domain.strip() for domain in domains_text.split('\n') if domain.strip()]
        if not domains:
            QMessageBox.warning(self, "输入错误", "没有有效的域名")
            return

        # 配置检测器
        self.comment_detector.max_pages = self.max_pages_spin.value()
        self.comment_detector.timeout = self.timeout_spin.value()

        if self.use_proxy_check.isChecked():
            # 设置代理
            proxy = self.proxy_manager.get_next_proxy()
            if proxy:
                self.comment_detector.set_proxy(proxy)

        # 重置结果表格
        self.detection_results_table.setRowCount(0)

        # 创建检测线程
        self.detection_thread = CommentDetectorThread(domains, self.comment_detector)
        self.detection_thread.progress_updated.connect(self.detect_progress.setValue)
        self.detection_thread.status_updated.connect(self.detect_status_label.setText)
        self.detection_thread.result_ready.connect(self.on_detection_result)
        self.detection_thread.error_occurred.connect(self.on_detection_error)
        self.detection_thread.start()

        # 更新按钮状态
        self.start_detection_btn.setEnabled(False)
        self.stop_detection_btn.setEnabled(True)

        self.detect_progress.setValue(0)
        self.detect_status_label.setText("开始检测...")

    def stop_comment_detection(self):
        """停止评论检测"""
        if hasattr(self, 'detection_thread') and self.detection_thread.isRunning():
            self.detection_thread.stop()
            self.detection_thread.wait()

        self.start_detection_btn.setEnabled(True)
        self.stop_detection_btn.setEnabled(False)
        self.detect_status_label.setText("检测已停止")

    def on_detection_result(self, result):
        """处理检测结果"""
        if result and result.get('has_comment_capability'):
            # 添加到结果表格
            row = self.detection_results_table.rowCount()
            self.detection_results_table.insertRow(row)

            self.detection_results_table.setItem(row, 0, QTableWidgetItem(result.get('domain', '')))
            self.detection_results_table.setItem(row, 1, QTableWidgetItem(result.get('cms_type', '未知')))
            self.detection_results_table.setItem(row, 2, QTableWidgetItem(str(result.get('score', 0))))
            self.detection_results_table.setItem(row, 3, QTableWidgetItem(str(len(result.get('article_urls', [])))))
            self.detection_results_table.setItem(row, 4, QTableWidgetItem(str(result.get('comment_forms', []))))
            self.detection_results_table.setItem(row, 5, QTableWidgetItem('检测成功'))

            # 保存到配置文件
            if self.save_to_db_check.isChecked():
                try:
                    site_data = {
                        'domain': result['domain'],
                        'title': result.get('title', ''),
                        'cms_type': result.get('cms_type', 'unknown'),
                        'can_comment': result.get('score', 0) > 50,
                        'requires_login': False,
                        'comment_score': result.get('score', 0),
                        'article_urls': result.get('article_urls', []),
                        'comment_forms': result.get('comment_forms', [])
                    }
                    self.config_manager.add_valid_site(site_data)
                except Exception as e:
                    print(f"保存检测结果到配置文件失败: {str(e)}")

    def on_detection_error(self, error_msg):
        """检测错误处理"""
        self.start_detection_btn.setEnabled(True)
        self.stop_detection_btn.setEnabled(False)
        QMessageBox.critical(self, "检测错误", error_msg)

    def export_detection_results(self):
        """导出检测结果"""
        if self.detection_results_table.rowCount() == 0:
            QMessageBox.warning(self, "导出错误", "没有可导出的检测结果")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出检测结果", "detection_results.csv",
            "CSV文件 (*.csv);;Excel文件 (*.xlsx)"
        )

        if file_path:
            try:
                data = []
                headers = ["域名", "CMS类型", "评分", "文章数", "表单信息", "状态"]

                for i in range(self.detection_results_table.rowCount()):
                    row_data = []
                    for j in range(self.detection_results_table.columnCount()):
                        item = self.detection_results_table.item(i, j)
                        row_data.append(item.text() if item else "")
                    data.append(row_data)

                import pandas as pd
                df = pd.DataFrame(data, columns=headers)

                if file_path.endswith('.xlsx'):
                    df.to_excel(file_path, index=False)
                else:
                    df.to_csv(file_path, index=False, encoding='utf-8-sig')

                QMessageBox.information(self, "导出成功", f"检测结果已导出到: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "导出错误", f"导出失败: {str(e)}")

    # AI生成功能方法
    def test_ai_api(self):
        """测试AI API"""
        api_key = self.api_key_edit.text().strip()
        if not api_key:
            QMessageBox.warning(self, "API错误", "请输入API密钥")
            return

        try:
            # 初始化AI生成器
            self.ai_generator = AICommentGenerator(api_key)

            # 测试API连接
            test_result = self.ai_generator.generate_comment(
                "测试文章", "positive", "short"
            )

            if test_result:
                QMessageBox.information(self, "API测试", "API连接成功！")
                self.statusBar().showMessage("AI API连接正常")
            else:
                QMessageBox.warning(self, "API测试", "API连接失败，请检查密钥")

        except Exception as e:
            QMessageBox.critical(self, "API错误", f"API测试失败: {str(e)}")

    def generate_ai_comments_batch(self):
        """批量生成AI评论"""
        if not self.ai_generator:
            QMessageBox.warning(self, "生成错误", "请先配置并测试API")
            return

        try:
            count = self.generate_count_spin.value()
            comment_type = self.comment_type_combo.currentText()
            length = self.comment_length_combo.currentText()
            keywords = self.keywords_edit.text().strip()

            # 生成评论
            comments = self.ai_generator.batch_generate(
                count=count,
                comment_type=comment_type,
                length=length,
                keywords=keywords.split(',') if keywords else None
            )

            if comments:
                # 显示生成的评论
                comment_text = '\n\n'.join([f"评论 {i+1}:\n{comment}" for i, comment in enumerate(comments)])
                self.generated_comments_text.setPlainText(comment_text)

                self.statusBar().showMessage(f"成功生成 {len(comments)} 条评论")
            else:
                QMessageBox.warning(self, "生成失败", "未能生成评论，请检查配置")

        except Exception as e:
            QMessageBox.critical(self, "生成错误", f"生成评论失败: {str(e)}")

    def save_generated_templates(self):
        """保存生成的评论为模板"""
        content = self.generated_comments_text.toPlainText().strip()
        if not content:
            QMessageBox.warning(self, "保存错误", "没有可保存的评论内容")
            return

        try:
            # 分割评论
            comments = []
            current_comment = ""

            for line in content.split('\n'):
                if line.startswith('评论 ') and ':' in line:
                    if current_comment:
                        comments.append(current_comment.strip())
                    current_comment = ""
                elif line.strip():
                    current_comment += line + '\n'

            if current_comment:
                comments.append(current_comment.strip())

            # 保存为模板
            template_name = f"AI生成模板_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            comment_type = self.comment_type_combo.currentText()

            # 直接保存评论到配置文件
            self.config_manager.generate_comments(comments)

            QMessageBox.information(self, "保存成功", f"已保存 {len(comments)} 个评论模板")

        except Exception as e:
            QMessageBox.critical(self, "保存错误", f"保存模板失败: {str(e)}")

    def generate_universal_comments(self):
        """生成10个通用评论模板"""
        try:
            # 显示进度
            self.generate_universal_btn.setEnabled(False)
            self.generate_universal_btn.setText("生成中...")

            # 调用AI生成器生成10个通用评论
            comments = self.ai_generator.generate_10_universal_comments()

            if comments:
                # 保存到配置管理器
                self.config_manager.generate_comments(comments)

                # 显示在界面上
                comments_text = '\n\n'.join([f"{i+1}. {comment}" for i, comment in enumerate(comments)])
                self.generated_comments_text.setPlainText(comments_text)

                self.statusBar().showMessage(f"已生成并保存 {len(comments)} 个通用评论模板")
                QMessageBox.information(self, "生成成功", f"已生成 {len(comments)} 个通用评论模板并保存到本地文件")
            else:
                QMessageBox.warning(self, "生成失败", "未能生成评论，请检查API配置")

        except Exception as e:
            QMessageBox.critical(self, "生成错误", f"生成评论时出错: {str(e)}")

        finally:
            # 恢复按钮状态
            self.generate_universal_btn.setEnabled(True)
            self.generate_universal_btn.setText("生成10个通用评论")

    def clear_generated_comments(self):
        """清空生成的评论"""
        self.generated_comments_text.clear()

    # 评论发布功能方法
    def load_comment_templates(self):
        """加载评论模板"""
        try:
            templates = self.config_manager.get_comments()

            if templates:
                self.comments_content_text.setPlainText('\n\n'.join(templates))
                self.statusBar().showMessage(f"已加载 {len(templates)} 个模板")
            else:
                QMessageBox.information(self, "加载模板", "没有找到可用的评论模板")

        except Exception as e:
            QMessageBox.critical(self, "加载错误", f"加载模板失败: {str(e)}")

    def generate_ai_comments(self):
        """为发布生成AI评论"""
        if not self.ai_generator:
            # 尝试从设置中获取API密钥
            api_key = self.api_key_edit.text().strip()
            if api_key:
                self.ai_generator = AICommentGenerator(api_key)
            else:
                QMessageBox.warning(self, "AI生成", "请先在AI生成标签页配置API")
                return

        try:
            # 生成10条评论用于发布
            comments = self.ai_generator.batch_generate(
                count=10,
                comment_type="positive",
                length="medium"
            )

            if comments:
                self.comments_content_text.setPlainText('\n\n'.join(comments))
                self.statusBar().showMessage(f"已生成 {len(comments)} 条评论")
            else:
                QMessageBox.warning(self, "生成失败", "未能生成评论")

        except Exception as e:
            QMessageBox.critical(self, "生成错误", f"生成评论失败: {str(e)}")

    def load_detected_sites(self):
        """加载已检测的网站"""
        try:
            sites = self.config_manager.get_valid_sites()

            self.target_sites_table.setRowCount(len(sites))

            for i, site in enumerate(sites):
                self.target_sites_table.setItem(i, 0, QTableWidgetItem(site['domain']))
                self.target_sites_table.setItem(i, 1, QTableWidgetItem(site.get('cms_type', '未知')))

                # 显示文章URL数量
                article_urls = site.get('article_urls', [])
                if isinstance(article_urls, list):
                    url_count = len(article_urls)
                    url_text = f"{url_count} 个URL"
                else:
                    url_text = "无URL"

                self.target_sites_table.setItem(i, 2, QTableWidgetItem(url_text))
                self.target_sites_table.setItem(i, 3, QTableWidgetItem('就绪'))
                self.target_sites_table.setItem(i, 4, QTableWidgetItem('选择'))

            self.statusBar().showMessage(f"已加载 {len(sites)} 个检测网站")

        except Exception as e:
            QMessageBox.critical(self, "加载错误", f"加载网站失败: {str(e)}")

    def start_comment_publishing(self):
        """开始评论发布"""
        # 检查评论内容
        comments_text = self.comments_content_text.toPlainText().strip()
        if not comments_text:
            QMessageBox.warning(self, "发布错误", "请输入要发布的评论内容")
            return

        # 检查目标网站
        if self.target_sites_table.rowCount() == 0:
            QMessageBox.warning(self, "发布错误", "请加载目标网站")
            return

        # 检查作者信息
        author_name = self.author_name_edit.text().strip()
        author_email = self.author_email_edit.text().strip()

        if not author_name or not author_email:
            QMessageBox.warning(self, "发布错误", "请填写作者姓名和邮箱")
            return

        try:
            # 初始化评论发布器
            if not self.comment_publisher:
                self.comment_publisher = CommentPublisher()

            # 配置发布参数
            publish_config = {
                'author_name': author_name,
                'author_email': author_email,
                'author_website': self.author_website_edit.text().strip(),
                'interval': self.publish_interval_spin.value(),
                'use_proxy': self.use_proxy_publish_check.isChecked(),
                'random_delay': self.random_delay_check.isChecked(),
                'simulate_human': self.simulate_human_check.isChecked()
            }

            # 准备评论列表
            comments = [comment.strip() for comment in comments_text.split('\n\n') if comment.strip()]

            # 准备目标网站列表
            target_sites = []
            all_sites = self.config_manager.get_valid_sites()
            for i in range(self.target_sites_table.rowCount()):
                domain = self.target_sites_table.item(i, 0).text()
                # 从配置中查找对应的网站信息
                site_info = next((site for site in all_sites if site['domain'] == domain), None)
                if site_info:
                    target_sites.append(site_info)

            if not target_sites:
                QMessageBox.warning(self, "发布错误", "没有有效的目标网站")
                return

            # 开始发布 (这里应该创建发布线程，简化处理)
            self.start_publishing_btn.setEnabled(False)
            self.pause_publishing_btn.setEnabled(True)
            self.stop_publishing_btn.setEnabled(True)

            self.publish_status_label.setText("开始发布评论...")
            self.statusBar().showMessage("评论发布已启动")

            # 实际发布逻辑应该在后台线程中执行
            QMessageBox.information(self, "发布启动", f"开始发布 {len(comments)} 条评论到 {len(target_sites)} 个网站")

        except Exception as e:
            QMessageBox.critical(self, "发布错误", f"启动发布失败: {str(e)}")

    def pause_comment_publishing(self):
        """暂停评论发布"""
        self.publish_status_label.setText("发布已暂停")
        self.statusBar().showMessage("评论发布已暂停")

    def stop_comment_publishing(self):
        """停止评论发布"""
        self.start_publishing_btn.setEnabled(True)
        self.pause_publishing_btn.setEnabled(False)
        self.stop_publishing_btn.setEnabled(False)

        self.publish_status_label.setText("发布已停止")
        self.statusBar().showMessage("评论发布已停止")

    # 设置功能方法
    def add_proxy_dialog(self):
        """添加代理对话框"""
        from PyQt5.QtWidgets import QDialog, QFormLayout, QDialogButtonBox

        dialog = QDialog(self)
        dialog.setWindowTitle("添加代理")
        dialog.setModal(True)

        layout = QFormLayout(dialog)

        # 代理配置输入
        host_edit = QLineEdit()
        port_edit = QLineEdit()
        type_combo = QComboBox()
        type_combo.addItems(["http", "https", "socks4", "socks5"])
        username_edit = QLineEdit()
        password_edit = QLineEdit()
        password_edit.setEchoMode(QLineEdit.Password)

        layout.addRow("主机地址:", host_edit)
        layout.addRow("端口:", port_edit)
        layout.addRow("类型:", type_combo)
        layout.addRow("用户名:", username_edit)
        layout.addRow("密码:", password_edit)

        # 按钮
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addRow(buttons)

        if dialog.exec_() == QDialog.Accepted:
            try:
                host = host_edit.text().strip()
                port = int(port_edit.text().strip())
                proxy_type = type_combo.currentText()
                username = username_edit.text().strip()
                password = password_edit.text().strip()

                if host and port:
                    proxy_info = {
                        'host': host,
                        'port': port,
                        'type': proxy_type,
                        'username': username if username else None,
                        'password': password if password else None
                    }

                    if self.proxy_manager.add_proxy(**proxy_info):
                        self.refresh_proxy_table()
                        QMessageBox.information(self, "添加成功", "代理已添加")
                    else:
                        QMessageBox.warning(self, "添加失败", "代理添加失败")
                else:
                    QMessageBox.warning(self, "输入错误", "请填写主机地址和端口")

            except ValueError:
                QMessageBox.warning(self, "输入错误", "端口必须是数字")
            except Exception as e:
                QMessageBox.critical(self, "添加错误", f"添加代理失败: {str(e)}")

    def refresh_proxy_table(self):
        """刷新代理表格"""
        try:
            # 使用get_best_proxies获取代理列表
            proxies = self.proxy_manager.get_best_proxies(count=20)

            self.proxy_table.setRowCount(len(proxies))

            for i, proxy in enumerate(proxies):
                self.proxy_table.setItem(i, 0, QTableWidgetItem(proxy.get('host', '')))
                self.proxy_table.setItem(i, 1, QTableWidgetItem(str(proxy.get('port', ''))))
                self.proxy_table.setItem(i, 2, QTableWidgetItem(proxy.get('type', '')))
                self.proxy_table.setItem(i, 3, QTableWidgetItem(proxy.get('status', '未测试')))
                self.proxy_table.setItem(i, 4, QTableWidgetItem(f"{proxy.get('response_time', 0):.2f}ms"))

        except Exception as e:
            print(f"刷新代理表格失败: {str(e)}")

    def test_all_proxies(self):
        """测试所有代理"""
        try:
            self.proxy_manager.test_all_proxies()
            self.refresh_proxy_table()
            QMessageBox.information(self, "测试完成", "代理测试完成")

        except Exception as e:
            QMessageBox.critical(self, "测试错误", f"代理测试失败: {str(e)}")

    def clear_all_proxies(self):
        """清空所有代理"""
        reply = QMessageBox.question(
            self, "确认清空",
            "确定要清空所有代理吗？",
            QMessageBox.Ok | QMessageBox.Cancel
        )

        if reply == QMessageBox.Ok:
            try:
                self.proxy_manager.clear_proxies()
                self.refresh_proxy_table()
                QMessageBox.information(self, "清空完成", "所有代理已清空")

            except Exception as e:
                QMessageBox.critical(self, "清空错误", f"清空代理失败: {str(e)}")

    def save_application_settings(self):
        """保存应用设置"""
        try:
            settings = {
                'database': {
                    'path': self.db_path_edit.text().strip()
                },
                'system': {
                    'log_level': self.log_level_combo.currentText(),
                    'max_threads': self.max_threads_spin.value(),
                    'auto_backup': self.auto_backup_check.isChecked(),
                    'auto_cleanup': self.auto_cleanup_check.isChecked()
                },
                'ai': {
                    'api_key': self.api_key_edit.text().strip(),
                    'model': self.model_combo.currentText()
                }
            }

            # 保存到配置文件
            os.makedirs('config', exist_ok=True)
            with open('config/settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

            QMessageBox.information(self, "保存成功", "设置已保存")

        except Exception as e:
            QMessageBox.critical(self, "保存错误", f"保存设置失败: {str(e)}")

    def reset_application_settings(self):
        """重置应用设置"""
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置所有设置吗？",
            QMessageBox.Ok | QMessageBox.Cancel
        )

        if reply == QMessageBox.Ok:
            try:
                # 重置界面控件到默认值
                self.db_path_edit.setText("data/comment_hunter.db")
                self.log_level_combo.setCurrentText("INFO")
                self.max_threads_spin.setValue(5)
                self.auto_backup_check.setChecked(False)
                self.auto_cleanup_check.setChecked(False)
                self.api_key_edit.clear()
                self.model_combo.setCurrentIndex(0)

                QMessageBox.information(self, "重置完成", "设置已重置为默认值")

            except Exception as e:
                QMessageBox.critical(self, "重置错误", f"重置设置失败: {str(e)}")

    def load_application_settings(self):
        """加载应用设置"""
        try:
            if os.path.exists('config/settings.json'):
                with open('config/settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # 加载数据库设置
                db_settings = settings.get('database', {})
                self.db_path_edit.setText(db_settings.get('path', 'data/comment_hunter.db'))

                # 加载系统设置
                system_settings = settings.get('system', {})
                self.log_level_combo.setCurrentText(system_settings.get('log_level', 'INFO'))
                self.max_threads_spin.setValue(system_settings.get('max_threads', 5))
                self.auto_backup_check.setChecked(system_settings.get('auto_backup', False))
                self.auto_cleanup_check.setChecked(system_settings.get('auto_cleanup', False))

                # 加载AI设置
                ai_settings = settings.get('ai', {})
                self.api_key_edit.setText(ai_settings.get('api_key', ''))
                model = ai_settings.get('model', 'Qwen/Qwen2.5-7B-Instruct')
                index = self.model_combo.findText(model)
                if index >= 0:
                    self.model_combo.setCurrentIndex(index)

        except Exception as e:
            print(f"加载设置失败: {str(e)}")

    def closeEvent(self, event):
        """程序关闭事件"""
        try:
            # 停止所有运行中的线程
            if hasattr(self, 'detection_thread') and self.detection_thread.isRunning():
                self.detection_thread.stop()
                self.detection_thread.wait()

            if hasattr(self, 'extract_thread') and self.extract_thread.isRunning():
                self.extract_thread.stop()
                self.extract_thread.wait()

            # 关闭配置管理器
            if hasattr(self, 'config_manager'):
                pass  # INI文件不需要显式关闭

            # 保存设置
            self.save_application_settings()

            event.accept()

        except Exception as e:
            print(f"程序关闭时发生错误: {str(e)}")
            event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("CommentHunter")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("CommentHunter Team")

    # 创建主窗口
    window = CommentHunterMainWindow()

    # 加载设置
    window.load_application_settings()

    # 刷新代理表格
    window.refresh_proxy_table()

    # 更新仪表板
    window.update_dashboard()

    # 显示窗口
    window.show()

    # 运行应用
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()