#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CommentHunter 本地配置管理模块
使用INI文件存储配置数据

<AUTHOR> Team
@version 1.0.0
"""

import os
import configparser
import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime


class ConfigManager:
    """本地INI配置管理器"""

    def __init__(self, config_dir: str = None):
        """
        初始化配置管理器

        Args:
            config_dir: 配置文件目录
        """
        if config_dir is None:
            config_dir = os.path.join(os.path.dirname(__file__), '..', 'data')

        self.config_dir = config_dir
        self.ensure_config_dir()

        # 3个主要配置文件路径
        self.valid_sites_file = os.path.join(config_dir, 'valid_sites.ini')      # 分析后有效的网址
        self.keywords_sites_file = os.path.join(config_dir, 'keywords_sites.ini')  # 关键词网址
        self.comments_file = os.path.join(config_dir, 'comments.ini')            # 生成的评论

        # 初始化配置文件
        self.init_config_files()
    
    def ensure_config_dir(self):
        """确保配置目录存在"""
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir, exist_ok=True)
    
    def init_config_files(self):
        """初始化配置文件"""
        # 初始化有效网站配置文件
        if not os.path.exists(self.valid_sites_file):
            valid_sites_config = configparser.ConfigParser()
            valid_sites_config['DEFAULT'] = {
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'description': 'Valid sites with comment functionality detected'
            }
            with open(self.valid_sites_file, 'w', encoding='utf-8') as f:
                valid_sites_config.write(f)

        # 初始化关键词网址配置文件
        if not os.path.exists(self.keywords_sites_file):
            keywords_sites_config = configparser.ConfigParser()
            keywords_sites_config['DEFAULT'] = {
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'description': 'Keywords and associated sites for publishing'
            }
            with open(self.keywords_sites_file, 'w', encoding='utf-8') as f:
                keywords_sites_config.write(f)

        # 初始化评论配置文件
        if not os.path.exists(self.comments_file):
            comments_config = configparser.ConfigParser()
            comments_config['DEFAULT'] = {
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'description': 'Generated comments with keyword variables'
            }
            with open(self.comments_file, 'w', encoding='utf-8') as f:
                comments_config.write(f)
    
    def _load_config(self, file_path: str) -> configparser.ConfigParser:
        """加载配置文件"""
        config = configparser.ConfigParser()
        if os.path.exists(file_path):
            config.read(file_path, encoding='utf-8')
        return config

    def _save_config(self, config: configparser.ConfigParser, file_path: str):
        """保存配置文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            config.write(f)

    # 有效网站管理
    def add_valid_site(self, site_data: Dict) -> str:
        """添加有效网站（分析后有评论功能的网站）"""
        config = self._load_config(self.valid_sites_file)
        site_id = f"site_{int(time.time() * 1000)}"

        config[site_id] = {
            'domain': site_data.get('domain', ''),
            'title': site_data.get('title', ''),
            'cms_type': site_data.get('cms_type', 'unknown'),
            'can_comment': str(site_data.get('can_comment', True)),
            'requires_login': str(site_data.get('requires_login', False)),
            'has_captcha': str(site_data.get('has_captcha', False)),
            'score': str(site_data.get('score', 0)),
            'article_urls': json.dumps(site_data.get('article_urls', [])),
            'comment_forms': json.dumps(site_data.get('comment_forms', [])),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

        self._save_config(config, self.valid_sites_file)
        return site_id

    def get_valid_sites(self, limit: int = 1000, offset: int = 0) -> List[Dict]:
        """获取有效网站列表"""
        config = self._load_config(self.valid_sites_file)
        sites = []

        sections = [s for s in config.sections() if s.startswith('site_')]
        sections = sections[offset:offset + limit]

        for section in sections:
            site_data = dict(config[section])
            site_data['id'] = section
            site_data['can_comment'] = site_data.get('can_comment', 'True').lower() == 'true'
            site_data['requires_login'] = site_data.get('requires_login', 'False').lower() == 'true'
            site_data['has_captcha'] = site_data.get('has_captcha', 'False').lower() == 'true'
            site_data['score'] = int(site_data.get('score', 0))

            # 解析JSON字段
            try:
                site_data['article_urls'] = json.loads(site_data.get('article_urls', '[]'))
            except:
                site_data['article_urls'] = []

            try:
                site_data['comment_forms'] = json.loads(site_data.get('comment_forms', '[]'))
            except:
                site_data['comment_forms'] = []

            sites.append(site_data)

        return sites

    def delete_valid_site(self, site_id: str) -> bool:
        """删除有效网站"""
        config = self._load_config(self.valid_sites_file)

        if site_id not in config:
            return False

        config.remove_section(site_id)
        self._save_config(config, self.valid_sites_file)
        return True
    
    # 关键词网址管理
    def add_keyword_site(self, keyword: str, site_url: str, description: str = '') -> str:
        """添加关键词网址"""
        config = self._load_config(self.keywords_sites_file)
        entry_id = f"entry_{int(time.time() * 1000)}"

        config[entry_id] = {
            'keyword': keyword,
            'site_url': site_url,
            'description': description,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

        self._save_config(config, self.keywords_sites_file)
        return entry_id

    def get_keyword_sites(self, limit: int = 1000, offset: int = 0) -> List[Dict]:
        """获取关键词网址列表"""
        config = self._load_config(self.keywords_sites_file)
        entries = []

        sections = [s for s in config.sections() if s.startswith('entry_')]
        sections = sections[offset:offset + limit]

        for section in sections:
            entry_data = dict(config[section])
            entry_data['id'] = section
            entries.append(entry_data)

        return entries

    def delete_keyword_site(self, entry_id: str) -> bool:
        """删除关键词网址"""
        config = self._load_config(self.keywords_sites_file)

        if entry_id not in config:
            return False

        config.remove_section(entry_id)
        self._save_config(config, self.keywords_sites_file)
        return True

    def get_keywords_list(self) -> List[str]:
        """获取所有关键词列表"""
        entries = self.get_keyword_sites()
        keywords = list(set([entry['keyword'] for entry in entries if entry.get('keyword')]))
        return sorted(keywords)

    # 评论管理
    def generate_comments(self, comments_list: List[str]):
        """生成评论到文件（重写整个文件）"""
        config = configparser.ConfigParser()
        config['DEFAULT'] = {
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'description': 'Generated 10 universal comments with keyword variables',
            'total_comments': str(len(comments_list))
        }

        for i, comment in enumerate(comments_list, 1):
            config[f'comment_{i}'] = {
                'content': comment,
                'created_at': datetime.now().isoformat()
            }

        self._save_config(config, self.comments_file)

    def get_comments(self, limit: int = 100, offset: int = 0) -> List[Dict]:
        """获取评论列表"""
        config = self._load_config(self.comments_file)
        comments = []

        sections = [s for s in config.sections() if s.startswith('comment_')]
        sections = sections[offset:offset + limit]

        for section in sections:
            comment_data = dict(config[section])
            comment_data['id'] = section
            comments.append(comment_data)

        return comments

    def get_random_comment(self) -> str:
        """获取随机评论"""
        comments = self.get_comments(limit=1000)
        if not comments:
            return "Great article about {keyword}! Thanks for sharing."

        import random
        return random.choice(comments)['content']
    
    # 统计数据
    def get_statistics(self) -> Dict:
        """获取统计数据"""
        valid_sites = len(self.get_valid_sites())
        keyword_sites = len(self.get_keyword_sites())
        comments = len(self.get_comments(limit=1000))

        return {
            'total_valid_sites': valid_sites,
            'total_keyword_sites': keyword_sites,
            'total_comments': comments,
            'last_updated': datetime.now().isoformat()
        }
    
    # 数据导出
    def export_data(self, data_type: str, format: str = 'json') -> str:
        """导出数据"""
        export_dir = os.path.join(self.config_dir, 'exports')
        if not os.path.exists(export_dir):
            os.makedirs(export_dir, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        if data_type == 'valid_sites':
            data = self.get_valid_sites()
            filename = f'valid_sites_export_{timestamp}.{format}'
        elif data_type == 'keyword_sites':
            data = self.get_keyword_sites()
            filename = f'keyword_sites_export_{timestamp}.{format}'
        elif data_type == 'comments':
            data = self.get_comments(limit=1000)
            filename = f'comments_export_{timestamp}.{format}'
        else:
            raise ValueError(f"不支持的数据类型: {data_type}")

        file_path = os.path.join(export_dir, filename)

        if format == 'json':
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        elif format == 'csv':
            import pandas as pd
            df = pd.DataFrame(data)
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
        else:
            raise ValueError(f"不支持的导出格式: {format}")

        return file_path

    # 数据备份
    def backup_data(self) -> str:
        """备份所有数据"""
        backup_dir = os.path.join(self.config_dir, 'backups')
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = os.path.join(backup_dir, f'backup_{timestamp}.json')

        backup_data = {
            'valid_sites': self.get_valid_sites(),
            'keyword_sites': self.get_keyword_sites(),
            'comments': self.get_comments(limit=1000),
            'statistics': self.get_statistics(),
            'backup_time': datetime.now().isoformat()
        }

        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2)

        return backup_file


# 创建全局配置管理器实例
config_manager = ConfigManager()
