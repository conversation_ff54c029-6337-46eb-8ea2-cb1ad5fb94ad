#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CommentHunter - 评论检测模块
功能：文章页面识别、CMS评论字段兼容、评论环境检测、智能筛选
"""

import re
import json
import time
import random
import requests
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Tuple, Union
from fake_useragent import UserAgent
from proxy_manager import ProxyManager


class CommentDetector:
    """评论检测器类"""

    def __init__(self, proxy_config: Optional[Dict] = None):
        """
        初始化评论检测器

        Args:
            proxy_config: 代理配置
        """
        self.proxy_config = proxy_config
        self.ua = UserAgent()
        self.session = requests.Session()

        # 初始化代理管理器
        self.proxy_manager = ProxyManager()

        # 设置代理 - 使用127.0.0.1:7897
        proxy_url = "http://127.0.0.1:7897"
        self.proxy_enabled = self._test_proxy(proxy_url)

        if self.proxy_enabled:
            self.session.proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
            print(f"评论检测器已配置代理: {proxy_url}")
        else:
            print(f"代理 {proxy_url} 不可用，将使用直连模式")
            self.session.proxies = {}

    def _test_proxy(self, proxy_url: str) -> bool:
        """测试代理是否可用"""
        try:
            test_session = requests.Session()
            test_session.proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
            response = test_session.get('http://httpbin.org/ip', timeout=5)
            return response.status_code == 200
        except Exception as e:
            print(f"代理测试失败: {e}")
            return False

        # CMS评论字段配置
        self.cms_configs = {
            'wordpress': {
                'comment_form_selectors': [
                    '#respond', '#commentform', '.comment-form',
                    'form[action*="wp-comments-post"]'
                ],
                'fields': {
                    'author': ['author', 'comment_author', 'name'],
                    'email': ['email', 'comment_author_email', 'mail'],
                    'url': ['url', 'comment_author_url', 'website'],
                    'comment': ['comment', 'comment_content', 'message']
                },
                'submit_selectors': [
                    '#submit', '.submit', 'input[type="submit"]',
                    'button[type="submit"]'
                ]
            },
            'zblog': {
                'comment_form_selectors': [
                    '#divCommentPost', '#frmSumbit', '.comment-form'
                ],
                'fields': {
                    'author': ['inpName', 'tbName', 'name'],
                    'email': ['inpEmail', 'tbMail', 'email'],
                    'url': ['inpHomePage', 'tbHomePage', 'url'],
                    'comment': ['txaArticle', 'tbComment', 'comment']
                },
                'submit_selectors': [
                    '#btnSumbit', '.submit-btn', 'input[type="submit"]'
                ]
            },
            'discuz': {
                'comment_form_selectors': [
                    '#fastpostform', '.fastpost', '#quickpostform'
                ],
                'fields': {
                    'author': ['username', 'author', 'name'],
                    'email': ['email', 'mail'],
                    'comment': ['message', 'fastpostmessage', 'comment']
                },
                'submit_selectors': [
                    '#fastpostsubmit', '.btn_submit', 'input[type="submit"]'
                ]
            },
            'generic': {
                'comment_form_selectors': [
                    'form[action*="comment"]', '.comment-form', '#comment-form',
                    'form[id*="comment"]', 'form[class*="comment"]'
                ],
                'fields': {
                    'author': ['name', 'author', 'username', 'user'],
                    'email': ['email', 'mail', 'e-mail'],
                    'url': ['url', 'website', 'homepage', 'site'],
                    'comment': ['comment', 'message', 'content', 'text', 'body']
                },
                'submit_selectors': [
                    'input[type="submit"]', 'button[type="submit"]',
                    '.submit', '.btn-submit', '#submit'
                ]
            }
        }

        # 文章页面URL模式
        self.article_patterns = [
            r'/post/',
            r'/article/',
            r'/blog/',
            r'/\?p=\d+',
            r'/\d{4}/\d{2}/\d{2}/',
            r'/\d+\.html',
            r'/article-\d+',
            r'/archives/',
            r'/entry/',
            r'/news/',
            r'/story/'
        ]

        # 登录检测关键词
        self.login_keywords = [
            'login', 'signin', 'log in', 'sign in', '登录', '登陆',
            'register', 'signup', 'sign up', '注册', '会员'
        ]

        # 验证码检测关键词
        self.captcha_keywords = [
            'captcha', 'verification', 'verify', 'code', '验证码',
            '验证', 'vcode', 'authcode', 'checkcode'
        ]

    def detect_comment_capability(self, domain: str, max_pages: int = 3) -> Dict:
        """
        检测域名的评论功能

        Args:
            domain: 域名
            max_pages: 最大检测页面数

        Returns:
            检测结果字典
        """
        result = {
            'domain': domain,
            'can_comment': False,
            'article_urls': [],
            'comment_forms': [],
            'cms_type': 'unknown',
            'requires_login': False,
            'has_captcha': False,
            'has_existing_comments': False,
            'score': 0,
            'error': None,
            'checked_at': time.strftime('%Y-%m-%d %H:%M:%S')
        }

        try:
            # 1. 获取网站首页
            base_url = f"https://{domain}" if not domain.startswith('http') else domain
            homepage_response = self._make_request(base_url)

            if not homepage_response:
                result['error'] = '无法访问网站首页'
                return result

            # 2. 查找文章页面链接
            article_urls = self._find_article_urls(homepage_response, base_url, max_pages)
            result['article_urls'] = article_urls

            if not article_urls:
                result['error'] = '未找到文章页面'
                return result

            # 3. 检测每个文章页面的评论功能
            comment_forms = []
            cms_types = []

            for article_url in article_urls[:max_pages]:
                article_result = self._detect_article_comments(article_url)
                if article_result['has_comment_form']:
                    comment_forms.append(article_result)
                    if article_result['cms_type'] != 'unknown':
                        cms_types.append(article_result['cms_type'])

                # 更新全局检测结果
                if article_result['requires_login']:
                    result['requires_login'] = True
                if article_result['has_captcha']:
                    result['has_captcha'] = True
                if article_result['has_existing_comments']:
                    result['has_existing_comments'] = True

            result['comment_forms'] = comment_forms

            # 4. 确定CMS类型
            if cms_types:
                # 选择出现最多的CMS类型
                result['cms_type'] = max(set(cms_types), key=cms_types.count)

            # 5. 评估是否可以评论
            result['can_comment'] = len(comment_forms) > 0

            # 6. 计算评分
            result['score'] = self._calculate_score(result)

        except Exception as e:
            result['error'] = f'检测过程出错: {str(e)}'

        return result

    def _make_request(self, url: str, timeout: int = 10) -> Optional[requests.Response]:
        """
        发起HTTP请求

        Args:
            url: 请求URL
            timeout: 超时时间

        Returns:
            响应对象或None
        """
        try:
            headers = {
                'User-Agent': self.ua.random,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            response = self.session.get(url, headers=headers, timeout=timeout, verify=False)
            response.raise_for_status()

            # 随机延迟，避免被检测
            time.sleep(random.uniform(1, 3))

            return response

        except Exception as e:
            print(f"请求失败 {url}: {str(e)}")
            return None

    def _find_article_urls(self, homepage_response: requests.Response, base_url: str, max_urls: int = 10) -> List[str]:
        """
        从首页查找文章页面链接

        Args:
            homepage_response: 首页响应
            base_url: 基础URL
            max_urls: 最大链接数

        Returns:
            文章URL列表
        """
        try:
            soup = BeautifulSoup(homepage_response.content, 'html.parser')
            article_urls = []

            # 查找所有链接
            links = soup.find_all('a', href=True)

            for link in links:
                href = link['href']

                # 转换为绝对URL
                if href.startswith('/'):
                    full_url = urljoin(base_url, href)
                elif href.startswith('http'):
                    full_url = href
                else:
                    continue

                # 检查是否匹配文章URL模式
                if self._is_article_url(full_url):
                    if full_url not in article_urls:
                        article_urls.append(full_url)

                        if len(article_urls) >= max_urls:
                            break

            return article_urls

        except Exception as e:
            print(f"查找文章链接失败: {str(e)}")
            return []

    def _is_article_url(self, url: str) -> bool:
        """
        判断URL是否为文章页面

        Args:
            url: URL

        Returns:
            是否为文章页面
        """
        # 排除一些明显不是文章的URL
        exclude_patterns = [
            r'/tag/', r'/category/', r'/author/', r'/page/',
            r'/search/', r'/archive/', r'/feed/', r'/rss/',
            r'\.css', r'\.js', r'\.jpg', r'\.png', r'\.gif',
            r'/wp-admin/', r'/admin/', r'/login/', r'/register/'
        ]

        for pattern in exclude_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return False

        # 检查是否匹配文章URL模式
        for pattern in self.article_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return True

        return False

    def _detect_article_comments(self, article_url: str) -> Dict:
        """
        检测单个文章页面的评论功能

        Args:
            article_url: 文章URL

        Returns:
            检测结果字典
        """
        result = {
            'url': article_url,
            'has_comment_form': False,
            'cms_type': 'unknown',
            'form_fields': {},
            'requires_login': False,
            'has_captcha': False,
            'has_existing_comments': False,
            'form_action': None,
            'form_method': 'POST'
        }

        try:
            response = self._make_request(article_url)
            if not response:
                return result

            soup = BeautifulSoup(response.content, 'html.parser')

            # 1. 检测CMS类型和评论表单
            cms_type, comment_form = self._detect_cms_and_form(soup)
            result['cms_type'] = cms_type

            if comment_form:
                result['has_comment_form'] = True
                result['form_action'] = comment_form.get('action', '')
                result['form_method'] = comment_form.get('method', 'POST').upper()

                # 2. 分析表单字段
                result['form_fields'] = self._analyze_form_fields(comment_form, cms_type)

                # 3. 检测登录要求
                result['requires_login'] = self._check_login_required(soup, comment_form)

                # 4. 检测验证码
                result['has_captcha'] = self._check_captcha_exists(soup, comment_form)

            # 5. 检测已有评论
            result['has_existing_comments'] = self._check_existing_comments(soup)

        except Exception as e:
            print(f"检测文章评论失败 {article_url}: {str(e)}")

        return result

    def _detect_cms_and_form(self, soup: BeautifulSoup) -> Tuple[str, Optional[BeautifulSoup]]:
        """
        检测CMS类型和评论表单

        Args:
            soup: BeautifulSoup对象

        Returns:
            (CMS类型, 评论表单元素)
        """
        # 按优先级检测CMS类型
        for cms_type, config in self.cms_configs.items():
            if cms_type == 'generic':
                continue

            for selector in config['comment_form_selectors']:
                form = soup.select_one(selector)
                if form:
                    return cms_type, form

        # 如果没有匹配特定CMS，尝试通用检测
        for selector in self.cms_configs['generic']['comment_form_selectors']:
            form = soup.select_one(selector)
            if form:
                return 'generic', form

        return 'unknown', None

    def _analyze_form_fields(self, form: BeautifulSoup, cms_type: str) -> Dict[str, str]:
        """
        分析表单字段

        Args:
            form: 表单元素
            cms_type: CMS类型

        Returns:
            字段映射字典
        """
        fields = {}
        config = self.cms_configs.get(cms_type, self.cms_configs['generic'])

        # 查找各种字段
        for field_type, field_names in config['fields'].items():
            for field_name in field_names:
                # 尝试不同的选择器
                selectors = [
                    f'input[name="{field_name}"]',
                    f'textarea[name="{field_name}"]',
                    f'#{field_name}',
                    f'.{field_name}'
                ]

                for selector in selectors:
                    element = form.select_one(selector)
                    if element:
                        fields[field_type] = {
                            'name': element.get('name', field_name),
                            'id': element.get('id', ''),
                            'type': element.name,
                            'required': element.has_attr('required'),
                            'placeholder': element.get('placeholder', '')
                        }
                        break

                if field_type in fields:
                    break

        return fields

    def _check_login_required(self, soup: BeautifulSoup, form: BeautifulSoup) -> bool:
        """
        检查是否需要登录

        Args:
            soup: 页面BeautifulSoup对象
            form: 表单元素

        Returns:
            是否需要登录
        """
        # 检查表单内是否有登录相关文本
        form_text = form.get_text().lower()
        for keyword in self.login_keywords:
            if keyword in form_text:
                return True

        # 检查是否有登录链接
        login_links = soup.find_all('a', href=True)
        for link in login_links:
            href = link['href'].lower()
            text = link.get_text().lower()

            for keyword in self.login_keywords:
                if keyword in href or keyword in text:
                    return True

        # 检查是否有用户信息显示（已登录状态）
        user_indicators = [
            'welcome', 'hello', '欢迎', '你好', 'logout', '退出',
            'profile', '个人资料', 'dashboard', '控制台'
        ]

        page_text = soup.get_text().lower()
        for indicator in user_indicators:
            if indicator in page_text:
                return False  # 已登录，不需要登录

        return False

    def _check_captcha_exists(self, soup: BeautifulSoup, form: BeautifulSoup) -> bool:
        """
        检查是否存在验证码

        Args:
            soup: 页面BeautifulSoup对象
            form: 表单元素

        Returns:
            是否存在验证码
        """
        # 检查表单内验证码相关元素
        captcha_elements = form.find_all(['img', 'input', 'div'],
                                       attrs={'src': True, 'class': True, 'id': True})

        for element in captcha_elements:
            # 检查属性值
            for attr_value in [element.get('src', ''), element.get('class', ''), element.get('id', '')]:
                if isinstance(attr_value, list):
                    attr_value = ' '.join(attr_value)

                attr_value = str(attr_value).lower()
                for keyword in self.captcha_keywords:
                    if keyword in attr_value:
                        return True

        # 检查表单文本内容
        form_text = form.get_text().lower()
        for keyword in self.captcha_keywords:
            if keyword in form_text:
                return True

        return False

    def _check_existing_comments(self, soup: BeautifulSoup) -> bool:
        """
        检查是否存在已有评论

        Args:
            soup: 页面BeautifulSoup对象

        Returns:
            是否存在已有评论
        """
        comment_selectors = [
            '.comment', '.comments', '#comments',
            '.comment-list', '.comment-content',
            '.reply', '.replies', '.comment-body'
        ]

        for selector in comment_selectors:
            elements = soup.select(selector)
            if elements and len(elements) > 0:
                # 检查是否包含实际评论内容
                for element in elements:
                    text = element.get_text().strip()
                    if len(text) > 10:  # 假设真实评论至少有10个字符
                        return True

        return False

    def _calculate_score(self, result: Dict) -> int:
        """
        计算评论能力评分

        Args:
            result: 检测结果

        Returns:
            评分 (0-100)
        """
        score = 0

        # 基础分：能否评论
        if result['can_comment']:
            score += 40

        # 表单数量加分
        form_count = len(result['comment_forms'])
        score += min(form_count * 10, 30)

        # CMS类型加分
        if result['cms_type'] != 'unknown':
            score += 15

        # 已有评论加分
        if result['has_existing_comments']:
            score += 10

        # 登录要求扣分
        if result['requires_login']:
            score -= 20

        # 验证码扣分
        if result['has_captcha']:
            score -= 15

        return max(0, min(100, score))

    def batch_detect(self, domains: List[str], max_pages: int = 3) -> List[Dict]:
        """
        批量检测域名评论功能

        Args:
            domains: 域名列表
            max_pages: 每个域名最大检测页面数

        Returns:
            检测结果列表
        """
        results = []

        for i, domain in enumerate(domains):
            print(f"正在检测 {i+1}/{len(domains)}: {domain}")

            result = self.detect_comment_capability(domain, max_pages)
            results.append(result)

            # 随机延迟，避免被封
            time.sleep(random.uniform(2, 5))

        return results