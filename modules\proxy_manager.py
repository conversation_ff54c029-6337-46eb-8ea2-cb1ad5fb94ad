#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CommentHunter - 代理管理模块
功能：代理池管理、连接测试、自动切换、网络优化
"""

import time
import random
import requests
import threading
from urllib.parse import urlparse
from typing import Dict, List, Optional, Tuple, Union
import json
import queue


class ProxyManager:
    """代理管理器类"""

    def __init__(self, config: Dict = None):
        """
        初始化代理管理器

        Args:
            config: 配置信息
        """
        self.config = config or {}
        self.proxies = []
        self.working_proxies = []
        self.failed_proxies = []
        self.current_proxy_index = 0
        self.proxy_stats = {}
        self.lock = threading.Lock()

        # 代理测试配置
        self.test_config = {
            'test_url': self.config.get('test_url', 'http://httpbin.org/ip'),
            'timeout': self.config.get('timeout', 10),
            'max_retries': self.config.get('max_retries', 3),
            'test_interval': self.config.get('test_interval', 300)  # 5分钟
        }

        # 支持的代理类型
        self.supported_types = ['http', 'https', 'socks4', 'socks5']

        # 代理轮换配置
        self.rotation_config = {
            'mode': self.config.get('rotation_mode', 'round_robin'),  # round_robin, random, weighted
            'failure_threshold': self.config.get('failure_threshold', 3),
            'recovery_time': self.config.get('recovery_time', 600)  # 10分钟
        }

    def add_proxy(self, proxy_url: str, proxy_type: str = 'http',
                  username: str = None, password: str = None) -> bool:
        """
        添加代理

        Args:
            proxy_url: 代理URL (格式: host:port)
            proxy_type: 代理类型
            username: 用户名
            password: 密码

        Returns:
            是否添加成功
        """
        try:
            if proxy_type not in self.supported_types:
                print(f"不支持的代理类型: {proxy_type}")
                return False

            # 解析代理URL
            if '://' not in proxy_url:
                proxy_url = f"{proxy_type}://{proxy_url}"

            parsed = urlparse(proxy_url)

            proxy_info = {
                'url': proxy_url,
                'type': proxy_type,
                'host': parsed.hostname,
                'port': parsed.port,
                'username': username or parsed.username,
                'password': password or parsed.password,
                'added_time': time.time(),
                'last_test_time': 0,
                'test_count': 0,
                'success_count': 0,
                'failure_count': 0,
                'avg_response_time': 0,
                'status': 'untested'  # untested, working, failed, recovering
            }

            # 构建完整的代理URL
            if proxy_info['username'] and proxy_info['password']:
                full_url = f"{proxy_type}://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['host']}:{proxy_info['port']}"
            else:
                full_url = f"{proxy_type}://{proxy_info['host']}:{proxy_info['port']}"

            proxy_info['full_url'] = full_url

            with self.lock:
                # 检查是否已存在
                for existing in self.proxies:
                    if existing['host'] == proxy_info['host'] and existing['port'] == proxy_info['port']:
                        print(f"代理已存在: {proxy_info['host']}:{proxy_info['port']}")
                        return False

                self.proxies.append(proxy_info)
                self.proxy_stats[full_url] = proxy_info

            print(f"添加代理成功: {proxy_info['host']}:{proxy_info['port']}")
            return True

        except Exception as e:
            print(f"添加代理失败: {str(e)}")
            return False

    def add_proxies_from_list(self, proxy_list: List[str], proxy_type: str = 'http') -> int:
        """
        从列表批量添加代理

        Args:
            proxy_list: 代理列表
            proxy_type: 代理类型

        Returns:
            成功添加的数量
        """
        success_count = 0

        for proxy_str in proxy_list:
            proxy_str = proxy_str.strip()
            if not proxy_str:
                continue

            # 解析不同格式的代理字符串
            if '@' in proxy_str:
                # 格式: username:password@host:port
                auth_part, host_part = proxy_str.split('@', 1)
                if ':' in auth_part:
                    username, password = auth_part.split(':', 1)
                else:
                    username, password = auth_part, None
            else:
                # 格式: host:port
                host_part = proxy_str
                username, password = None, None

            if self.add_proxy(host_part, proxy_type, username, password):
                success_count += 1

        print(f"批量添加代理完成: {success_count}/{len(proxy_list)}")
        return success_count

    def load_proxies_from_file(self, file_path: str, proxy_type: str = 'http') -> int:
        """
        从文件加载代理

        Args:
            file_path: 文件路径
            proxy_type: 代理类型

        Returns:
            成功加载的数量
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            proxy_list = [line.strip() for line in lines if line.strip()]
            return self.add_proxies_from_list(proxy_list, proxy_type)

        except Exception as e:
            print(f"从文件加载代理失败: {str(e)}")
            return 0

    def test_proxy(self, proxy_info: Dict) -> Dict:
        """
        测试单个代理

        Args:
            proxy_info: 代理信息

        Returns:
            测试结果
        """
        result = {
            'proxy_url': proxy_info['full_url'],
            'success': False,
            'response_time': 0,
            'error': None,
            'ip_info': None
        }

        start_time = time.time()

        try:
            proxies = {
                'http': proxy_info['full_url'],
                'https': proxy_info['full_url']
            }

            response = requests.get(
                self.test_config['test_url'],
                proxies=proxies,
                timeout=self.test_config['timeout'],
                verify=False
            )

            response.raise_for_status()
            response_time = time.time() - start_time

            # 尝试解析IP信息
            try:
                ip_info = response.json()
            except:
                ip_info = {'ip': 'unknown'}

            result.update({
                'success': True,
                'response_time': response_time,
                'ip_info': ip_info
            })

            # 更新代理统计
            with self.lock:
                proxy_info['last_test_time'] = time.time()
                proxy_info['test_count'] += 1
                proxy_info['success_count'] += 1
                proxy_info['avg_response_time'] = (
                    (proxy_info['avg_response_time'] * (proxy_info['success_count'] - 1) + response_time) /
                    proxy_info['success_count']
                )
                proxy_info['status'] = 'working'

        except Exception as e:
            result['error'] = str(e)

            # 更新失败统计
            with self.lock:
                proxy_info['last_test_time'] = time.time()
                proxy_info['test_count'] += 1
                proxy_info['failure_count'] += 1

                # 判断是否标记为失败
                if proxy_info['failure_count'] >= self.rotation_config['failure_threshold']:
                    proxy_info['status'] = 'failed'
                else:
                    proxy_info['status'] = 'working'

        return result

    def test_all_proxies(self, max_workers: int = 10) -> Dict:
        """
        测试所有代理

        Args:
            max_workers: 最大并发数

        Returns:
            测试结果统计
        """
        if not self.proxies:
            return {
                'total': 0,
                'working': 0,
                'failed': 0,
                'results': []
            }

        print(f"开始测试 {len(self.proxies)} 个代理...")

        results = []
        working_count = 0

        # 使用线程池进行并发测试
        import concurrent.futures

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有测试任务
            future_to_proxy = {
                executor.submit(self.test_proxy, proxy): proxy
                for proxy in self.proxies
            }

            # 收集结果
            for future in concurrent.futures.as_completed(future_to_proxy):
                proxy = future_to_proxy[future]
                try:
                    result = future.result()
                    results.append(result)

                    if result['success']:
                        working_count += 1
                        print(f"✓ {proxy['host']}:{proxy['port']} - {result['response_time']:.2f}s")
                    else:
                        print(f"✗ {proxy['host']}:{proxy['port']} - {result['error']}")

                except Exception as e:
                    print(f"✗ {proxy['host']}:{proxy['port']} - 测试异常: {str(e)}")

        # 更新工作代理列表
        self._update_working_proxies()

        stats = {
            'total': len(self.proxies),
            'working': working_count,
            'failed': len(self.proxies) - working_count,
            'results': results
        }

        print(f"测试完成: {working_count}/{len(self.proxies)} 个代理可用")
        return stats

    def _update_working_proxies(self):
        """更新工作代理列表"""
        with self.lock:
            self.working_proxies = [
                proxy for proxy in self.proxies
                if proxy['status'] == 'working'
            ]
            self.failed_proxies = [
                proxy for proxy in self.proxies
                if proxy['status'] == 'failed'
            ]

    def get_next_proxy(self) -> Optional[Dict]:
        """
        获取下一个可用代理

        Returns:
            代理信息或None
        """
        if not self.working_proxies:
            self._update_working_proxies()

        if not self.working_proxies:
            return None

        with self.lock:
            if self.rotation_config['mode'] == 'round_robin':
                proxy = self.working_proxies[self.current_proxy_index]
                self.current_proxy_index = (self.current_proxy_index + 1) % len(self.working_proxies)

            elif self.rotation_config['mode'] == 'random':
                proxy = random.choice(self.working_proxies)

            elif self.rotation_config['mode'] == 'weighted':
                # 基于响应时间的加权选择
                weights = []
                for p in self.working_proxies:
                    # 响应时间越短，权重越高
                    weight = 1.0 / (p['avg_response_time'] + 0.1)
                    weights.append(weight)

                proxy = random.choices(self.working_proxies, weights=weights)[0]

            else:
                proxy = self.working_proxies[0]

        return proxy

    def get_proxy_for_requests(self) -> Optional[Dict]:
        """
        获取用于requests的代理配置

        Returns:
            代理配置字典或None
        """
        proxy = self.get_next_proxy()
        if not proxy:
            return None

        return {
            'http': proxy['full_url'],
            'https': proxy['full_url']
        }

    def mark_proxy_failed(self, proxy_url: str):
        """
        标记代理为失败

        Args:
            proxy_url: 代理URL
        """
        with self.lock:
            if proxy_url in self.proxy_stats:
                proxy_info = self.proxy_stats[proxy_url]
                proxy_info['failure_count'] += 1

                if proxy_info['failure_count'] >= self.rotation_config['failure_threshold']:
                    proxy_info['status'] = 'failed'
                    print(f"代理标记为失败: {proxy_url}")

                    # 更新工作代理列表
                    self._update_working_proxies()

    def recover_failed_proxies(self):
        """恢复失败的代理（重新测试）"""
        current_time = time.time()
        recovery_candidates = []

        with self.lock:
            for proxy in self.failed_proxies:
                if (current_time - proxy['last_test_time']) > self.rotation_config['recovery_time']:
                    recovery_candidates.append(proxy)
                    proxy['status'] = 'recovering'

        if recovery_candidates:
            print(f"尝试恢复 {len(recovery_candidates)} 个失败的代理...")

            for proxy in recovery_candidates:
                result = self.test_proxy(proxy)
                if result['success']:
                    print(f"代理恢复成功: {proxy['host']}:{proxy['port']}")
                else:
                    print(f"代理恢复失败: {proxy['host']}:{proxy['port']}")

            self._update_working_proxies()

    def get_proxy_stats(self) -> Dict:
        """
        获取代理统计信息

        Returns:
            统计信息字典
        """
        with self.lock:
            total = len(self.proxies)
            working = len(self.working_proxies)
            failed = len(self.failed_proxies)

            # 计算平均响应时间
            avg_response_times = [
                proxy['avg_response_time']
                for proxy in self.working_proxies
                if proxy['avg_response_time'] > 0
            ]

            avg_response_time = sum(avg_response_times) / len(avg_response_times) if avg_response_times else 0

            # 按类型统计
            type_stats = {}
            for proxy in self.proxies:
                proxy_type = proxy['type']
                if proxy_type not in type_stats:
                    type_stats[proxy_type] = {'total': 0, 'working': 0}

                type_stats[proxy_type]['total'] += 1
                if proxy['status'] == 'working':
                    type_stats[proxy_type]['working'] += 1

        return {
            'total_proxies': total,
            'working_proxies': working,
            'failed_proxies': failed,
            'success_rate': (working / total * 100) if total > 0 else 0,
            'avg_response_time': avg_response_time,
            'type_stats': type_stats,
            'current_proxy_index': self.current_proxy_index
        }

    def remove_proxy(self, proxy_url: str) -> bool:
        """
        移除代理

        Args:
            proxy_url: 代理URL

        Returns:
            是否移除成功
        """
        with self.lock:
            # 从主列表中移除
            for i, proxy in enumerate(self.proxies):
                if proxy['full_url'] == proxy_url:
                    removed_proxy = self.proxies.pop(i)

                    # 从统计中移除
                    if proxy_url in self.proxy_stats:
                        del self.proxy_stats[proxy_url]

                    print(f"移除代理: {removed_proxy['host']}:{removed_proxy['port']}")

                    # 更新工作代理列表
                    self._update_working_proxies()
                    return True

        return False

    def clear_all_proxies(self):
        """清空所有代理"""
        with self.lock:
            self.proxies.clear()
            self.working_proxies.clear()
            self.failed_proxies.clear()
            self.proxy_stats.clear()
            self.current_proxy_index = 0

        print("已清空所有代理")

    def save_proxies_to_file(self, file_path: str, include_stats: bool = True) -> bool:
        """
        保存代理到文件

        Args:
            file_path: 文件路径
            include_stats: 是否包含统计信息

        Returns:
            是否保存成功
        """
        try:
            data = {
                'proxies': self.proxies,
                'config': self.config,
                'saved_time': time.time()
            }

            if include_stats:
                data['stats'] = self.get_proxy_stats()

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            print(f"代理数据已保存到: {file_path}")
            return True

        except Exception as e:
            print(f"保存代理数据失败: {str(e)}")
            return False

    def load_proxies_from_json(self, file_path: str) -> bool:
        """
        从JSON文件加载代理

        Args:
            file_path: 文件路径

        Returns:
            是否加载成功
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if 'proxies' in data:
                with self.lock:
                    self.proxies = data['proxies']

                    # 重建统计字典
                    self.proxy_stats = {}
                    for proxy in self.proxies:
                        self.proxy_stats[proxy['full_url']] = proxy

                self._update_working_proxies()
                print(f"从文件加载了 {len(self.proxies)} 个代理")
                return True
            else:
                print("文件格式错误：缺少proxies字段")
                return False

        except Exception as e:
            print(f"加载代理数据失败: {str(e)}")
            return False

    def export_working_proxies(self, file_path: str, format_type: str = 'txt') -> bool:
        """
        导出可用代理

        Args:
            file_path: 文件路径
            format_type: 文件格式 (txt/json)

        Returns:
            是否导出成功
        """
        try:
            if not self.working_proxies:
                print("没有可用的代理可导出")
                return False

            if format_type == 'txt':
                with open(file_path, 'w', encoding='utf-8') as f:
                    for proxy in self.working_proxies:
                        if proxy['username'] and proxy['password']:
                            line = f"{proxy['username']}:{proxy['password']}@{proxy['host']}:{proxy['port']}\n"
                        else:
                            line = f"{proxy['host']}:{proxy['port']}\n"
                        f.write(line)

            elif format_type == 'json':
                export_data = []
                for proxy in self.working_proxies:
                    export_data.append({
                        'host': proxy['host'],
                        'port': proxy['port'],
                        'type': proxy['type'],
                        'username': proxy['username'],
                        'password': proxy['password'],
                        'avg_response_time': proxy['avg_response_time'],
                        'success_rate': proxy['success_count'] / proxy['test_count'] if proxy['test_count'] > 0 else 0
                    })

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

            print(f"已导出 {len(self.working_proxies)} 个可用代理到: {file_path}")
            return True

        except Exception as e:
            print(f"导出代理失败: {str(e)}")
            return False

    def start_auto_recovery(self, interval: int = 300):
        """
        启动自动恢复线程

        Args:
            interval: 检查间隔（秒）
        """
        def recovery_worker():
            while True:
                try:
                    time.sleep(interval)
                    self.recover_failed_proxies()
                except Exception as e:
                    print(f"自动恢复线程异常: {str(e)}")

        recovery_thread = threading.Thread(target=recovery_worker, daemon=True)
        recovery_thread.start()
        print(f"自动恢复线程已启动，检查间隔: {interval}秒")

    def get_best_proxies(self, count: int = 5) -> List[Dict]:
        """
        获取最佳代理列表

        Args:
            count: 返回数量

        Returns:
            最佳代理列表
        """
        if not self.working_proxies:
            return []

        # 按响应时间和成功率排序
        sorted_proxies = sorted(
            self.working_proxies,
            key=lambda p: (
                p['avg_response_time'],
                -p['success_count'] / max(p['test_count'], 1)
            )
        )

        return sorted_proxies[:count]

    def __str__(self) -> str:
        """字符串表示"""
        stats = self.get_proxy_stats()
        return f"ProxyManager(total={stats['total_proxies']}, working={stats['working_proxies']}, failed={stats['failed_proxies']})"