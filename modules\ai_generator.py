#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CommentHunter - AI评论生成模块
功能：集成SiliconFlow API、批量生成评论、内容优化、模板管理
"""

import json
import time
import random
import requests
from typing import Dict, List, Optional, Union
import re


class AICommentGenerator:
    """AI评论生成器类"""

    def __init__(self, api_config: Dict):
        """
        初始化AI评论生成器

        Args:
            api_config: API配置信息
        """
        self.api_config = api_config
        self.api_key = api_config.get('api_key', '')
        self.base_url = api_config.get('base_url', 'https://api.siliconflow.cn/v1')
        self.model = api_config.get('model', 'Qwen/Qwen2.5-7B-Instruct')
        self.max_tokens = api_config.get('max_tokens', 200)
        self.temperature = api_config.get('temperature', 0.8)

        # 请求会话
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        })

        # 评论模板类型 (英文版本)
        self.comment_templates = {
            'positive': [
                "Great article! {content_point}. Thanks for sharing!",
                "Very useful content, especially the part about {content_point}.",
                "Learned a lot, the perspective on {content_point} is very insightful.",
                "The author's analysis is thorough, {content_point} is indeed true.",
                "Excellent article! {content_point} gave me new thoughts."
            ],
            'question': [
                "Regarding {content_point}, I'd like to ask a question: {question}",
                "Great article, but about {content_point}, {question}",
                "After reading, I have a question: in the case of {content_point}, {question}",
                "Want to learn more about {content_point}, {question}"
            ],
            'experience': [
                "I've also encountered similar situations, {content_point} is indeed important.",
                "Based on my experience, {content_point} is particularly crucial.",
                "I've faced this problem before, the {content_point} approach is very practical.",
                "In actual practice, {content_point} indeed needs attention."
            ],
            'discussion': [
                "Regarding {content_point}, I think we can consider it from another angle.",
                "About {content_point}, I have some different views.",
                "Besides {content_point}, are there other solutions?",
                "The {content_point} mentioned in the article makes sense, adding some personal thoughts."
            ]
        }

        # 内容关键词提取模式 (英文版本)
        self.keyword_patterns = [
            r'(?:about|regarding|concerning)\s+([a-zA-Z\s]{2,20})',
            r'([a-zA-Z\s]{2,20})\s+(?:is important|is crucial|is useful)',
            r'(?:through|using|by)\s+([a-zA-Z\s]{2,20})',
            r'([a-zA-Z\s]{2,20})\s+(?:method|technique|strategy|approach)'
        ]

    def generate_comment(self, article_content: str, comment_type: str = 'auto',
                        custom_prompt: str = None) -> Dict:
        """
        生成单条评论

        Args:
            article_content: 文章内容
            comment_type: 评论类型 (positive/question/experience/discussion/auto)
            custom_prompt: 自定义提示词

        Returns:
            生成结果字典
        """
        result = {
            'success': False,
            'comment': '',
            'comment_type': comment_type,
            'tokens_used': 0,
            'generation_time': 0,
            'error': None
        }

        start_time = time.time()

        try:
            # 1. 预处理文章内容
            processed_content = self._preprocess_content(article_content)

            if not processed_content:
                result['error'] = '文章内容为空或无法处理'
                return result

            # 2. 构建提示词
            if custom_prompt:
                prompt = custom_prompt.format(content=processed_content)
            else:
                prompt = self._build_prompt(processed_content, comment_type)

            # 3. 调用API生成评论
            api_response = self._call_api(prompt)

            if not api_response['success']:
                result['error'] = api_response['error']
                return result

            # 4. 后处理生成的评论
            raw_comment = api_response['content']
            processed_comment = self._postprocess_comment(raw_comment)

            result.update({
                'success': True,
                'comment': processed_comment,
                'tokens_used': api_response.get('tokens_used', 0),
                'generation_time': time.time() - start_time
            })

        except Exception as e:
            result['error'] = f'生成评论时出错: {str(e)}'

        return result

    def batch_generate(self, articles_data: List[Dict], batch_size: int = 5,
                      delay_range: tuple = (1, 3)) -> List[Dict]:
        """
        批量生成评论

        Args:
            articles_data: 文章数据列表，每个包含content和可选的comment_type
            batch_size: 批次大小
            delay_range: 请求间延迟范围（秒）

        Returns:
            生成结果列表
        """
        results = []
        total = len(articles_data)

        for i in range(0, total, batch_size):
            batch = articles_data[i:i + batch_size]
            batch_results = []

            print(f"正在处理批次 {i//batch_size + 1}/{(total + batch_size - 1)//batch_size}")

            for j, article_data in enumerate(batch):
                print(f"  生成评论 {i + j + 1}/{total}")

                content = article_data.get('content', '')
                comment_type = article_data.get('comment_type', 'auto')
                custom_prompt = article_data.get('custom_prompt')

                result = self.generate_comment(content, comment_type, custom_prompt)
                result['article_index'] = i + j
                batch_results.append(result)

                # 请求间延迟
                if j < len(batch) - 1:
                    delay = random.uniform(*delay_range)
                    time.sleep(delay)

            results.extend(batch_results)

            # 批次间延迟
            if i + batch_size < total:
                delay = random.uniform(3, 8)
                print(f"  批次完成，等待 {delay:.1f} 秒...")
                time.sleep(delay)

        return results

    def _preprocess_content(self, content: str) -> str:
        """
        预处理文章内容

        Args:
            content: 原始内容

        Returns:
            处理后的内容
        """
        if not content:
            return ""

        # 清理HTML标签
        content = re.sub(r'<[^>]+>', '', content)

        # 清理多余的空白字符
        content = re.sub(r'\s+', ' ', content).strip()

        # 截取前500个字符作为摘要
        if len(content) > 500:
            content = content[:500] + "..."

        return content

    def _build_prompt(self, content: str, comment_type: str) -> str:
        """
        构建AI提示词

        Args:
            content: 文章内容
            comment_type: 评论类型

        Returns:
            构建的提示词
        """
        # 提取内容关键点
        content_points = self._extract_content_points(content)

        if comment_type == 'auto':
            # 自动选择评论类型
            comment_type = random.choice(['positive', 'question', 'experience', 'discussion'])

        # 基础提示词模板 (英文版本)
        base_prompts = {
            'positive': f"""
Generate a positive and supportive comment based on the following article content. Requirements:
1. Comment length should be 50-150 characters in English
2. Friendly and natural tone
3. Show appreciation and recognition for the article
4. May mention specific points from the article
5. Must be in English

Article content: {content}

Generate comment:
""",
            'question': f"""
Generate a question-based comment based on the following article content. Requirements:
1. Comment length should be 60-180 characters in English
2. Ask relevant questions or express curiosity
3. Humble and inquisitive tone
4. Questions should be related to the article content
5. Must be in English

Article content: {content}

Generate comment:
""",
            'experience': f"""
Generate an experience-sharing comment based on the following article content. Requirements:
1. Comment length should be 80-220 characters in English
2. Share relevant personal experience or insights
3. Sincere and practical tone
4. Should resonate with the article theme
5. Must be in English

Article content: {content}

Generate comment:
""",
            'discussion': f"""
Generate a discussion-oriented comment based on the following article content. Requirements:
1. Comment length should be 100-280 characters in English
2. Offer different perspectives or additional thoughts
3. Rational and constructive tone
4. Encourage further discussion
5. Must be in English

Article content: {content}

Generate comment:
"""
        }

        return base_prompts.get(comment_type, base_prompts['positive'])

    def _extract_content_points(self, content: str) -> List[str]:
        """
        提取文章内容要点

        Args:
            content: 文章内容

        Returns:
            内容要点列表
        """
        points = []

        # 使用正则表达式提取关键词
        for pattern in self.keyword_patterns:
            matches = re.findall(pattern, content)
            points.extend(matches)

        # 去重并限制数量
        unique_points = list(set(points))[:5]

        return unique_points

    def _call_api(self, prompt: str) -> Dict:
        """
        调用SiliconFlow API

        Args:
            prompt: 提示词

        Returns:
            API响应结果
        """
        result = {
            'success': False,
            'content': '',
            'tokens_used': 0,
            'error': None
        }

        try:
            payload = {
                'model': self.model,
                'messages': [
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ],
                'max_tokens': self.max_tokens,
                'temperature': self.temperature,
                'stream': False
            }

            response = self.session.post(
                f'{self.base_url}/chat/completions',
                json=payload,
                timeout=30
            )

            response.raise_for_status()
            data = response.json()

            if 'choices' in data and len(data['choices']) > 0:
                content = data['choices'][0]['message']['content'].strip()
                result.update({
                    'success': True,
                    'content': content,
                    'tokens_used': data.get('usage', {}).get('total_tokens', 0)
                })
            else:
                result['error'] = 'API返回数据格式错误'

        except requests.exceptions.RequestException as e:
            result['error'] = f'API请求失败: {str(e)}'
        except json.JSONDecodeError as e:
            result['error'] = f'API响应解析失败: {str(e)}'
        except Exception as e:
            result['error'] = f'API调用出错: {str(e)}'

        return result

    def _postprocess_comment(self, raw_comment: str) -> str:
        """
        后处理生成的评论

        Args:
            raw_comment: 原始评论

        Returns:
            处理后的评论
        """
        if not raw_comment:
            return ""

        # 清理多余的引号和标点
        comment = raw_comment.strip().strip('"\'')

        # 移除可能的前缀（如"评论："）
        prefixes = ['评论：', '回复：', '留言：', 'Comment:', 'Reply:']
        for prefix in prefixes:
            if comment.startswith(prefix):
                comment = comment[len(prefix):].strip()

        # 确保评论以合适的标点结尾
        if comment and not comment[-1] in '。！？.!?':
            comment += '。'

        # 长度限制
        if len(comment) > 200:
            comment = comment[:197] + '...'

        return comment

    def generate_template_comment(self, article_content: str, template_type: str,
                                 custom_variables: Dict = None) -> Dict:
        """
        使用模板生成评论

        Args:
            article_content: 文章内容
            template_type: 模板类型
            custom_variables: 自定义变量

        Returns:
            生成结果
        """
        result = {
            'success': False,
            'comment': '',
            'template_type': template_type,
            'error': None
        }

        try:
            if template_type not in self.comment_templates:
                result['error'] = f'不支持的模板类型: {template_type}'
                return result

            # 提取内容要点
            content_points = self._extract_content_points(article_content)

            if not content_points:
                content_points = ['这个话题']

            # 随机选择模板
            template = random.choice(self.comment_templates[template_type])

            # 准备变量
            variables = {
                'content_point': random.choice(content_points),
                'question': self._generate_question(content_points[0] if content_points else '这个话题')
            }

            # 合并自定义变量
            if custom_variables:
                variables.update(custom_variables)

            # 填充模板
            try:
                comment = template.format(**variables)
                result.update({
                    'success': True,
                    'comment': comment
                })
            except KeyError as e:
                result['error'] = f'模板变量缺失: {str(e)}'

        except Exception as e:
            result['error'] = f'模板生成失败: {str(e)}'

        return result

    def _generate_question(self, topic: str) -> str:
        """
        生成相关问题

        Args:
            topic: 话题

        Returns:
            生成的问题
        """
        question_templates = [
            f"有没有更多关于{topic}的资料推荐？",
            f"在实际应用{topic}时需要注意什么？",
            f"{topic}的效果如何评估？",
            f"除了{topic}，还有其他类似的方法吗？",
            f"对于初学者，如何更好地理解{topic}？"
        ]

        return random.choice(question_templates)

    def optimize_comment(self, comment: str, optimization_type: str = 'natural') -> Dict:
        """
        优化评论内容

        Args:
            comment: 原始评论
            optimization_type: 优化类型 (natural/formal/casual)

        Returns:
            优化结果
        """
        result = {
            'success': False,
            'original_comment': comment,
            'optimized_comment': '',
            'optimization_type': optimization_type,
            'error': None
        }

        try:
            optimization_prompts = {
                'natural': f"""
Optimize the following comment to make it more natural and conversational in English:

Original comment: {comment}

Optimization requirements:
1. Keep the original meaning unchanged
2. Make the language more natural and fluent
3. Suitable tone for online comments
4. Length should be 80%-120% of the original comment
5. Must be in English

Optimized comment:
""",
                'formal': f"""
Optimize the following comment to make it more formal and professional in English:

Original comment: {comment}

Optimization requirements:
1. Keep the original meaning unchanged
2. Use more formal expressions
3. Make the logic clearer
4. Suitable for academic or business contexts
5. Must be in English

Optimized comment:
""",
                'casual': f"""
Optimize the following comment to make it more relaxed and casual in English:

Original comment: {comment}

Optimization requirements:
1. Keep the original meaning unchanged
2. Use a more relaxed and friendly tone
3. Can appropriately use internet language
4. Increase approachability
5. Must be in English

Optimized comment:
"""
            }

            prompt = optimization_prompts.get(optimization_type, optimization_prompts['natural'])
            api_response = self._call_api(prompt)

            if api_response['success']:
                optimized = self._postprocess_comment(api_response['content'])
                result.update({
                    'success': True,
                    'optimized_comment': optimized
                })
            else:
                result['error'] = api_response['error']

        except Exception as e:
            result['error'] = f'评论优化失败: {str(e)}'

        return result

    def get_generation_stats(self, results: List[Dict]) -> Dict:
        """
        获取生成统计信息

        Args:
            results: 生成结果列表

        Returns:
            统计信息
        """
        if not results:
            return {
                'total_count': 0,
                'success_count': 0,
                'failure_count': 0,
                'success_rate': 0,
                'total_tokens': 0,
                'avg_generation_time': 0,
                'comment_types': {}
            }

        success_results = [r for r in results if r.get('success', False)]

        # 统计评论类型分布
        comment_types = {}
        for result in results:
            comment_type = result.get('comment_type', 'unknown')
            comment_types[comment_type] = comment_types.get(comment_type, 0) + 1

        return {
            'total_count': len(results),
            'success_count': len(success_results),
            'failure_count': len(results) - len(success_results),
            'success_rate': len(success_results) / len(results) * 100,
            'total_tokens': sum(r.get('tokens_used', 0) for r in success_results),
            'avg_generation_time': sum(r.get('generation_time', 0) for r in success_results) / len(success_results) if success_results else 0,
            'comment_types': comment_types
        }

    def save_comments_to_file(self, results: List[Dict], file_path: str,
                             format_type: str = 'json') -> bool:
        """
        保存评论到文件

        Args:
            results: 生成结果列表
            file_path: 文件路径
            format_type: 文件格式 (json/txt/csv)

        Returns:
            是否保存成功
        """
        try:
            if format_type == 'json':
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)

            elif format_type == 'txt':
                with open(file_path, 'w', encoding='utf-8') as f:
                    for i, result in enumerate(results):
                        if result.get('success'):
                            f.write(f"评论 {i+1}:\n{result['comment']}\n\n")

            elif format_type == 'csv':
                import csv
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(['序号', '评论内容', '评论类型', '生成状态', '错误信息'])

                    for i, result in enumerate(results):
                        writer.writerow([
                            i + 1,
                            result.get('comment', ''),
                            result.get('comment_type', ''),
                            '成功' if result.get('success') else '失败',
                            result.get('error', '')
                        ])

            return True

        except Exception as e:
            print(f"保存文件失败: {str(e)}")
            return False

    def generate_10_universal_comments(self) -> List[str]:
        """
        生成10个通用英文评论模板，包含{keyword}变量

        Returns:
            包含10个评论的列表
        """
        try:
            # 构建英文prompt
            prompt = """Generate 10 diverse, high-quality English comment templates for blog articles and websites. Requirements:

1. Each comment must be in English
2. Include the variable {keyword} that will be replaced with actual keywords
3. Vary in length (short, medium, longer)
4. Cover different types: positive feedback, questions, experiences, suggestions
5. Sound natural and engaging like genuine human comments
6. Avoid mentioning specific URLs or website names
7. Be suitable for various topics and websites

Format: Return only the comments, one per line, without numbering.

Example style:
- "Great insights about {keyword}! This really helped me understand the topic better."
- "I've been researching {keyword} for a while, and this article provides exactly what I was looking for."

Generate 10 similar high-quality English comment templates:"""

            # 发送请求到SiliconFlow API
            response = self.session.post(
                f'{self.base_url}/chat/completions',
                json={
                    'model': self.model,
                    'messages': [
                        {
                            'role': 'user',
                            'content': prompt
                        }
                    ],
                    'max_tokens': 800,
                    'temperature': 0.8
                },
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                generated_text = result['choices'][0]['message']['content'].strip()

                # 解析生成的评论
                comments = []
                lines = generated_text.split('\n')

                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#') and not line.startswith('//'):
                        # 清理可能的编号
                        import re
                        line = re.sub(r'^\d+[\.\)]\s*', '', line)
                        line = re.sub(r'^[-\*]\s*', '', line)
                        line = re.sub(r'^["\']|["\']$', '', line)  # 移除引号

                        if line and '{keyword}' in line:
                            comments.append(line)

                # 如果生成的评论不足10个，用默认模板补充
                while len(comments) < 10:
                    default_comments = [
                        "Excellent article about {keyword}! Very informative and well-written.",
                        "Thanks for sharing this detailed guide on {keyword}. It's exactly what I needed.",
                        "I've been working with {keyword} for years, and this is one of the best explanations I've seen.",
                        "Great insights on {keyword}. Looking forward to implementing these suggestions.",
                        "This {keyword} tutorial is fantastic. Clear, concise, and practical.",
                        "As a beginner in {keyword}, I found this article extremely helpful.",
                        "Could you provide more examples of {keyword} implementation?",
                        "In my experience with {keyword}, I've found that consistency is key.",
                        "I suggest adding more visual examples to illustrate {keyword} concepts.",
                        "The {keyword} topic is complex but you've explained it clearly."
                    ]

                    for default_comment in default_comments:
                        if len(comments) < 10:
                            comments.append(default_comment)
                        else:
                            break

                return comments[:10]  # 确保只返回10个

            else:
                print(f"API请求失败: {response.status_code}")
                return self._get_default_10_comments()

        except Exception as e:
            print(f"生成评论时出错: {str(e)}")
            return self._get_default_10_comments()

    def _get_default_10_comments(self) -> List[str]:
        """获取默认的10个英文评论模板"""
        return [
            "Excellent article about {keyword}! Very informative and well-written.",
            "Thanks for sharing this detailed guide on {keyword}. It's exactly what I needed.",
            "I've been working with {keyword} for years, and this is one of the best explanations I've seen.",
            "Great insights on {keyword}. Looking forward to implementing these suggestions.",
            "This {keyword} tutorial is fantastic. Clear, concise, and practical.",
            "As a beginner in {keyword}, I found this article extremely helpful.",
            "Could you provide more examples of {keyword} implementation?",
            "In my experience with {keyword}, I've found that consistency is key.",
            "I suggest adding more visual examples to illustrate {keyword} concepts.",
            "The {keyword} topic is complex but you've explained it clearly."
        ]


# 创建全局AI生成器实例
def create_ai_generator(config_file: str = None) -> 'AICommentGenerator':
    """
    创建AI生成器实例

    Args:
        config_file: 配置文件路径

    Returns:
        AICommentGenerator实例
    """
    if config_file:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        ai_config = config.get('ai', {})
    else:
        # 默认配置
        ai_config = {
            'api_key': 'sk-fehvttrlgxgxwomdpegycdldtqlumyxsawgvgblaquzhhrfz',
            'base_url': 'https://api.siliconflow.cn/v1',
            'model': 'Qwen/Qwen2.5-7B-Instruct',
            'max_tokens': 200,
            'temperature': 0.8
        }

    return AICommentGenerator(ai_config)