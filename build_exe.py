#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CommentHunter 打包脚本
使用 PyInstaller 将 Python 应用打包为可执行文件

<AUTHOR> Team
@version 1.0.0
"""

import os
import sys
import shutil
import subprocess
import json
from pathlib import Path

# 项目配置
PROJECT_NAME = "CommentHunter"
VERSION = "1.0.0"
AUTHOR = "CommentHunter Team"
DESCRIPTION = "网站评论分析与发布工具"

# 路径配置
BASE_DIR = Path(__file__).parent
DIST_DIR = BASE_DIR / "dist"
BUILD_DIR = BASE_DIR / "build"
RESOURCES_DIR = BASE_DIR / "resources"
CONFIG_DIR = BASE_DIR / "config"
MODULES_DIR = BASE_DIR / "modules"

# PyInstaller 配置
PYINSTALLER_OPTIONS = [
    "--name", PROJECT_NAME,
    "--onedir",  # 使用单目录模式，便于包含资源文件
    "--windowed",  # Windows下隐藏控制台窗口
    "--noconfirm",  # 覆盖输出目录
    "--clean",  # 清理临时文件
    "--distpath", str(DIST_DIR),
    "--workpath", str(BUILD_DIR),
    "--specpath", str(BASE_DIR),
]

# 需要包含的数据文件
DATA_FILES = [
    (str(CONFIG_DIR), "config"),
    (str(RESOURCES_DIR), "resources"),
]

# 需要包含的隐藏导入
HIDDEN_IMPORTS = [
    "PyQt5.QtCore",
    "PyQt5.QtGui", 
    "PyQt5.QtWidgets",
    "requests",
    "beautifulsoup4",
    "selenium",
    "fake_useragent",
    "pandas",
    "openpyxl",
    "lxml",
    "urllib3",
    "sqlite3",
    "json",
    "threading",
    "queue",
    "datetime",
    "random",
    "time",
    "re",
    "os",
    "sys",
    "pathlib",
]

# 排除的模块（减小打包体积）
EXCLUDES = [
    "tkinter",
    "matplotlib",
    "numpy",
    "scipy",
    "IPython",
    "jupyter",
    "notebook",
    "pytest",
    "unittest",
    "doctest",
]

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print(f"  {PROJECT_NAME} v{VERSION} 打包工具")
    print(f"  {DESCRIPTION}")
    print(f"  作者: {AUTHOR}")
    print("=" * 60)
    print()

def check_requirements():
    """检查打包环境"""
    print("🔍 检查打包环境...")
    
    # 检查 Python 版本
    python_version = sys.version_info
    if python_version < (3, 8):
        print("❌ Python 版本过低，需要 Python 3.8+")
        return False
    print(f"✅ Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查 PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ 未安装 PyInstaller，请运行: pip install pyinstaller")
        return False
    
    # 检查主程序文件
    main_file = BASE_DIR / "main.py"
    if not main_file.exists():
        print(f"❌ 主程序文件不存在: {main_file}")
        return False
    print(f"✅ 主程序文件: {main_file}")
    
    # 检查依赖文件
    requirements_file = BASE_DIR / "requirements.txt"
    if requirements_file.exists():
        print(f"✅ 依赖文件: {requirements_file}")
    else:
        print(f"⚠️  依赖文件不存在: {requirements_file}")
    
    # 检查资源目录
    if RESOURCES_DIR.exists():
        print(f"✅ 资源目录: {RESOURCES_DIR}")
    else:
        print(f"⚠️  资源目录不存在: {RESOURCES_DIR}")
        RESOURCES_DIR.mkdir(exist_ok=True)
    
    # 检查配置目录
    if CONFIG_DIR.exists():
        print(f"✅ 配置目录: {CONFIG_DIR}")
    else:
        print(f"❌ 配置目录不存在: {CONFIG_DIR}")
        return False
    
    print()
    return True

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = [DIST_DIR, BUILD_DIR]
    
    for dir_path in dirs_to_clean:
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"✅ 已清理: {dir_path}")
    
    # 清理 spec 文件
    spec_files = list(BASE_DIR.glob("*.spec"))
    for spec_file in spec_files:
        spec_file.unlink()
        print(f"✅ 已删除: {spec_file}")
    
    print()

def create_icon():
    """创建应用图标"""
    print("🎨 处理应用图标...")
    
    icon_file = RESOURCES_DIR / "logo.ico"
    if not icon_file.exists():
        print(f"⚠️  图标文件不存在: {icon_file}")
        # 创建默认图标目录
        RESOURCES_DIR.mkdir(exist_ok=True)
        print(f"✅ 已创建资源目录: {RESOURCES_DIR}")
        return None
    
    print(f"✅ 图标文件: {icon_file}")
    return str(icon_file)

def build_application():
    """构建应用程序"""
    print("🔨 开始构建应用程序...")
    
    # 准备 PyInstaller 命令
    cmd = ["pyinstaller"] + PYINSTALLER_OPTIONS
    
    # 添加图标
    icon_path = create_icon()
    if icon_path:
        cmd.extend(["--icon", icon_path])
    
    # 添加数据文件
    for src, dst in DATA_FILES:
        if Path(src).exists():
            cmd.extend(["--add-data", f"{src};{dst}"])
            print(f"✅ 添加数据文件: {src} -> {dst}")
    
    # 添加隐藏导入
    for module in HIDDEN_IMPORTS:
        cmd.extend(["--hidden-import", module])
    
    # 添加排除模块
    for module in EXCLUDES:
        cmd.extend(["--exclude-module", module])
    
    # 添加主程序文件
    cmd.append(str(BASE_DIR / "main.py"))
    
    print(f"🚀 执行命令: {' '.join(cmd)}")
    print()
    
    # 执行构建
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def post_build_tasks():
    """构建后处理"""
    print("📦 执行构建后处理...")
    
    app_dir = DIST_DIR / PROJECT_NAME
    if not app_dir.exists():
        print(f"❌ 应用目录不存在: {app_dir}")
        return False
    
    # 创建数据目录
    data_dir = app_dir / "data"
    data_dir.mkdir(exist_ok=True)
    print(f"✅ 创建数据目录: {data_dir}")
    
    # 创建日志目录
    logs_dir = app_dir / "logs"
    logs_dir.mkdir(exist_ok=True)
    print(f"✅ 创建日志目录: {logs_dir}")
    
    # 创建临时目录
    temp_dir = app_dir / "temp"
    temp_dir.mkdir(exist_ok=True)
    print(f"✅ 创建临时目录: {temp_dir}")
    
    # 创建导出目录
    exports_dir = app_dir / "exports"
    exports_dir.mkdir(exist_ok=True)
    print(f"✅ 创建导出目录: {exports_dir}")
    
    # 复制 README 文件
    readme_file = BASE_DIR / "README.md"
    if readme_file.exists():
        shutil.copy2(readme_file, app_dir / "README.md")
        print(f"✅ 复制 README: {readme_file}")
    
    # 创建版本信息文件
    version_info = {
        "name": PROJECT_NAME,
        "version": VERSION,
        "author": AUTHOR,
        "description": DESCRIPTION,
        "build_time": str(Path(__file__).stat().st_mtime),
        "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    }
    
    version_file = app_dir / "version.json"
    with open(version_file, 'w', encoding='utf-8') as f:
        json.dump(version_info, f, ensure_ascii=False, indent=2)
    print(f"✅ 创建版本信息: {version_file}")
    
    # 创建启动脚本
    create_launcher_script(app_dir)
    
    print()
    return True

def create_launcher_script(app_dir):
    """创建启动脚本"""
    print("📝 创建启动脚本...")
    
    # Windows 批处理脚本
    bat_content = f"""@echo off
title {PROJECT_NAME} v{VERSION}
echo 正在启动 {PROJECT_NAME}...
echo.
"{PROJECT_NAME}.exe"
if errorlevel 1 (
    echo.
    echo 程序异常退出，按任意键关闭窗口...
    pause >nul
)
"""
    
    bat_file = app_dir / f"启动{PROJECT_NAME}.bat"
    with open(bat_file, 'w', encoding='gbk') as f:
        f.write(bat_content)
    print(f"✅ 创建批处理脚本: {bat_file}")
    
    # PowerShell 脚本
    ps1_content = f"""# {PROJECT_NAME} v{VERSION} 启动脚本
Write-Host "正在启动 {PROJECT_NAME}..." -ForegroundColor Green
Write-Host ""

try {{
    & ".\\{PROJECT_NAME}.exe"
}} catch {{
    Write-Host "程序启动失败: $_" -ForegroundColor Red
    Read-Host "按回车键退出"
}}
"""
    
    ps1_file = app_dir / f"启动{PROJECT_NAME}.ps1"
    with open(ps1_file, 'w', encoding='utf-8') as f:
        f.write(ps1_content)
    print(f"✅ 创建PowerShell脚本: {ps1_file}")

def create_installer():
    """创建安装包（可选）"""
    print("📦 创建安装包...")
    
    # 这里可以集成 NSIS 或其他安装包制作工具
    # 暂时跳过，直接提供目录形式的分发包
    
    print("⚠️  安装包制作功能待实现")
    print("💡 当前提供目录形式的分发包")
    print()

def show_build_summary():
    """显示构建摘要"""
    print("📊 构建摘要")
    print("-" * 40)
    
    app_dir = DIST_DIR / PROJECT_NAME
    if app_dir.exists():
        # 计算目录大小
        total_size = sum(f.stat().st_size for f in app_dir.rglob('*') if f.is_file())
        size_mb = total_size / (1024 * 1024)
        
        print(f"✅ 构建成功")
        print(f"📁 输出目录: {app_dir}")
        print(f"📏 包大小: {size_mb:.1f} MB")
        print(f"📄 主程序: {app_dir / f'{PROJECT_NAME}.exe'}")
        
        # 列出主要文件
        main_files = [
            f"{PROJECT_NAME}.exe",
            "config/",
            "resources/",
            "data/",
            "README.md",
            "version.json"
        ]
        
        print(f"📋 主要文件:")
        for file_name in main_files:
            file_path = app_dir / file_name
            if file_path.exists():
                print(f"   ✅ {file_name}")
            else:
                print(f"   ❌ {file_name}")
    else:
        print("❌ 构建失败")
    
    print("-" * 40)
    print()

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_requirements():
        print("❌ 环境检查失败，请解决上述问题后重试")
        return 1
    
    # 清理构建目录
    clean_build()
    
    # 构建应用程序
    if not build_application():
        print("❌ 应用程序构建失败")
        return 1
    
    # 构建后处理
    if not post_build_tasks():
        print("❌ 构建后处理失败")
        return 1
    
    # 创建安装包（可选）
    create_installer()
    
    # 显示构建摘要
    show_build_summary()
    
    print("🎉 打包完成！")
    print(f"💡 可执行文件位于: {DIST_DIR / PROJECT_NAME}")
    print()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
